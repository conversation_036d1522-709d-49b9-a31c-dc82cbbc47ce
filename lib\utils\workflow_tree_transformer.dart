import '../models/workflow_tree_node.dart';

class WorkflowTreeTransformer {
  static List<WorkflowTreeNode> transformWorkflowToTree(Map<String, dynamic> workflowData) {
    List<WorkflowTreeNode> treeNodes = [];

    try {
      print('🌳 WorkflowTreeTransformer: Starting transformation...');
      print('🌳 Input data keys: ${workflowData.keys.toList()}');
      
      // Extract the GO (Global Objective) data
      final goData = workflowData['GO'] ?? workflowData;
      print('🌳 GO data type: ${goData.runtimeType}');
      
      // Handle different data types
      Map<String, dynamic> goMap;
      if (goData is Map<String, dynamic>) {
        goMap = goData;
        print('🌳 GO data keys: ${goMap.keys.toList()}');
      } else {
        // Convert object to map using reflection or create a simple structure
        print('🌳 GO data is not a Map, converting...');
        goMap = _convertObjectToMap(goData);
        print('🌳 Converted GO data keys: ${goMap.keys.toList()}');
      }
      
      // Create root node - Global Objective
      final globalObjective = _createGlobalObjectiveNode(goMap);
      print('🌳 Created global objective: ${globalObjective.text}');
      
      // Create pathway nodes as children of global objective
      final pathwayNodes = _createPathwayNodes(goMap);
      print('🌳 Created ${pathwayNodes.length} pathway nodes');
      
      // Update global objective with pathway children
      final rootNode = globalObjective.copyWith(
        children: pathwayNodes,
        isExpanded: true,
      );
      
      treeNodes.add(rootNode);
      print('🌳 Transformation complete: ${treeNodes.length} root nodes');
      
      return treeNodes;
    } catch (e) {
      print('🌳 Error transforming workflow to tree: $e');
      print('🌳 Stack trace: ${StackTrace.current}');
      return [];
    }
  }

  static WorkflowTreeNode _createGlobalObjectiveNode(Map<String, dynamic> goData) {
    final globalObjectives = goData['global_objectives'] ?? {};
    
    return WorkflowTreeNode(
      id: 'go_${globalObjectives['go_id'] ?? 'unknown'}',
      text: globalObjectives['name'] ?? 'Workflow Process',
      altText: globalObjectives['description'] ?? 'Global workflow objective',
      level: 0,
      sequence: 0,
      nodeType: WorkflowNodeType.globalObjective,
      isExpanded: true,
      metadata: globalObjectives,
    );
  }

  static List<WorkflowTreeNode> _createPathwayNodes(Map<String, dynamic> goData) {
    List<WorkflowTreeNode> pathwayNodes = [];
    
    final pathwayDefinitions = goData['pathway_definitions'] as List<dynamic>? ?? [];
    final localObjectives = goData['local_objectives_list'] as List<dynamic>? ?? [];
    final processFlow = goData['process_flow'] as List<dynamic>? ?? [];
    
    // Create pathway nodes
    for (int i = 0; i < pathwayDefinitions.length; i++) {
      final pathway = pathwayDefinitions[i];
      final pathwaySteps = pathway['steps'] as List<dynamic>? ?? [];
      
      // Create local objective nodes for this pathway
      final loNodes = _createLocalObjectiveNodesForPathway(
        pathwaySteps, 
        localObjectives, 
        processFlow,
        i,
      );
      
      final pathwayNode = WorkflowTreeNode(
        id: 'pathway_${pathway['id'] ?? i}',
        text: pathway['pathway_name'] ?? 'Pathway ${i + 1}',
        altText: 'Contains ${loNodes.length} steps',
        level: 1,
        sequence: i,
        nodeType: WorkflowNodeType.pathway,
        isExpanded: true,
        children: loNodes,
        metadata: pathway,
      );
      
      pathwayNodes.add(pathwayNode);
    }
    
    // Add business rules as a separate section
    final businessRulesNode = _createBusinessRulesNode(goData);
    if (businessRulesNode != null) {
      pathwayNodes.add(businessRulesNode);
    }
    
    return pathwayNodes;
  }

  static List<WorkflowTreeNode> _createLocalObjectiveNodesForPathway(
    List<dynamic> pathwaySteps,
    List<dynamic> allLocalObjectives,
    List<dynamic> processFlow,
    int pathwayIndex,
  ) {
    List<WorkflowTreeNode> loNodes = [];
    
    for (int stepIndex = 0; stepIndex < pathwaySteps.length; stepIndex++) {
      final step = pathwaySteps[stepIndex].toString();
      
      // Handle parallel steps (e.g., "(LO-7, LO-9)")
      if (step.startsWith('(') && step.endsWith(')')) {
        final parallelNode = _createParallelNode(step, allLocalObjectives, processFlow, pathwayIndex, stepIndex);
        if (parallelNode != null) {
          loNodes.add(parallelNode);
        }
      } else {
        // Regular step
        final loNode = _createLocalObjectiveNode(step, allLocalObjectives, processFlow, pathwayIndex, stepIndex);
        if (loNode != null) {
          loNodes.add(loNode);
        }
      }
    }
    
    return loNodes;
  }

  static WorkflowTreeNode? _createLocalObjectiveNode(
    String stepRef,
    List<dynamic> allLocalObjectives,
    List<dynamic> processFlow,
    int pathwayIndex,
    int stepIndex,
  ) {
    // Extract LO number from step reference (e.g., "LO-1" -> 1)
    final loNumber = _extractLoNumber(stepRef);
    if (loNumber == null) return null;
    
    // Find the corresponding local objective
    final lo = allLocalObjectives.firstWhere(
      (obj) => obj['lo_number'] == loNumber,
      orElse: () => null,
    );
    
    if (lo == null) return null;
    
    // Find corresponding process flow for additional details
    final flowItem = processFlow.firstWhere(
      (flow) => flow['lo_id'] == lo['id'],
      orElse: () => null,
    );
    
    final isTerminal = lo['terminal'] == true;
    final actorType = lo['actor_type'] ?? 'UNKNOWN';
    
    return WorkflowTreeNode(
      id: 'lo_${lo['id']}_pathway_$pathwayIndex',
      text: lo['lo_name'] ?? 'Local Objective $loNumber',
      altText: flowItem?['description'] ?? 'Actor: $actorType',
      level: 2,
      sequence: stepIndex,
      nodeType: WorkflowNodeType.localObjective,
      actorType: actorType,
      isTerminal: isTerminal,
      metadata: {
        'lo_data': lo,
        'flow_data': flowItem,
      },
    );
  }

  static WorkflowTreeNode? _createParallelNode(
    String parallelStep,
    List<dynamic> allLocalObjectives,
    List<dynamic> processFlow,
    int pathwayIndex,
    int stepIndex,
  ) {
    // Parse parallel steps: "(LO-7, LO-9)" -> ["LO-7", "LO-9"]
    final cleanStep = parallelStep.replaceAll(RegExp(r'[()]'), '');
    final parallelSteps = cleanStep.split(',').map((s) => s.trim()).toList();
    
    // Create child nodes for each parallel step
    List<WorkflowTreeNode> parallelChildren = [];
    for (int i = 0; i < parallelSteps.length; i++) {
      final childNode = _createLocalObjectiveNode(
        parallelSteps[i], 
        allLocalObjectives, 
        processFlow, 
        pathwayIndex, 
        i,
      );
      if (childNode != null) {
        parallelChildren.add(childNode.copyWith(level: 3));
      }
    }
    
    if (parallelChildren.isEmpty) return null;
    
    return WorkflowTreeNode(
      id: 'parallel_${pathwayIndex}_$stepIndex',
      text: 'Parallel Execution',
      altText: '${parallelChildren.length} concurrent steps',
      level: 2,
      sequence: stepIndex,
      nodeType: WorkflowNodeType.parallel,
      isParallel: true,
      isExpanded: true,
      children: parallelChildren,
      metadata: {
        'parallel_steps': parallelSteps,
      },
    );
  }

  static WorkflowTreeNode? _createBusinessRulesNode(Map<String, dynamic> goData) {
    final businessRules = goData['business_rules'] as List<dynamic>? ?? [];
    
    if (businessRules.isEmpty) return null;
    
    // Create business rule child nodes
    List<WorkflowTreeNode> ruleNodes = [];
    for (int i = 0; i < businessRules.length; i++) {
      final rule = businessRules[i];
      
      final ruleNode = WorkflowTreeNode(
        id: 'rule_${rule['id'] ?? i}',
        text: rule['rule_name'] ?? 'Business Rule ${i + 1}',
        altText: rule['rule_description'] ?? 'Validation rule',
        level: 2,
        sequence: i,
        nodeType: WorkflowNodeType.businessRule,
        metadata: rule,
      );
      
      ruleNodes.add(ruleNode);
    }
    
    return WorkflowTreeNode(
      id: 'business_rules_section',
      text: 'Business Rules',
      altText: '${businessRules.length} validation rules',
      level: 1,
      sequence: 999, // Put at the end
      nodeType: WorkflowNodeType.businessRule,
      isExpanded: false,
      children: ruleNodes,
      metadata: {
        'section_type': 'business_rules',
        'rule_count': businessRules.length,
      },
    );
  }

  static int? _extractLoNumber(String stepRef) {
    // Extract number from "LO-1", "LO-2", etc.
    final match = RegExp(r'LO-(\d+)').firstMatch(stepRef);
    if (match != null) {
      return int.tryParse(match.group(1) ?? '');
    }
    return null;
  }

  // Helper method to convert object to map
  static Map<String, dynamic> _convertObjectToMap(dynamic obj) {
    try {
      // If it's already a map, return it
      if (obj is Map<String, dynamic>) {
        return obj;
      }
      
      // Try to convert using toString and basic reflection
      Map<String, dynamic> result = {};
      
      // Check if object has common workflow properties
      if (obj != null) {
        // Try to access common properties using reflection or dynamic access
        try {
          // Use dynamic access to get properties
          if (obj.globalObjectives != null) {
            result['global_objectives'] = _objectToMap(obj.globalObjectives);
          }
          if (obj.localObjectivesList != null) {
            result['local_objectives_list'] = _listToMapList(obj.localObjectivesList);
          }
          if (obj.pathwayDefinitions != null) {
            result['pathway_definitions'] = _listToMapList(obj.pathwayDefinitions);
          }
          if (obj.businessRules != null) {
            result['business_rules'] = _listToMapList(obj.businessRules);
          }
          if (obj.processFlow != null) {
            result['process_flow'] = _listToMapList(obj.processFlow);
          }
        } catch (e) {
          print('🌳 Error accessing object properties: $e');
        }
      }
      
      return result;
    } catch (e) {
      print('🌳 Error converting object to map: $e');
      return {};
    }
  }
  
  // Helper to convert object to map
  static Map<String, dynamic> _objectToMap(dynamic obj) {
    if (obj == null) return {};
    if (obj is Map<String, dynamic>) return obj;
    
    Map<String, dynamic> result = {};
    try {
      // Try to access common properties
      if (obj.name != null) result['name'] = obj.name;
      if (obj.description != null) result['description'] = obj.description;
      if (obj.id != null) result['id'] = obj.id;
      if (obj.goId != null) result['go_id'] = obj.goId;
    } catch (e) {
      print('🌳 Error converting object: $e');
    }
    return result;
  }
  
  // Helper to convert list to map list
  static List<Map<String, dynamic>> _listToMapList(dynamic list) {
    if (list == null) return [];
    if (list is! List) return [];
    
    return list.map((item) => _objectToMap(item)).toList();
  }

  // Helper method to create a simple tree structure for testing
  static List<WorkflowTreeNode> createSampleTree() {
    return [
      WorkflowTreeNode(
        id: 'sample_workflow',
        text: 'Sample Workflow Process',
        altText: 'A sample workflow for testing',
        level: 0,
        sequence: 0,
        nodeType: WorkflowNodeType.globalObjective,
        isExpanded: true,
        children: [
          WorkflowTreeNode(
            id: 'pathway_1',
            text: 'Standard Approval Path',
            altText: 'Normal approval process',
            level: 1,
            sequence: 0,
            nodeType: WorkflowNodeType.pathway,
            isExpanded: true,
            children: [
              WorkflowTreeNode(
                id: 'lo_1',
                text: 'Submit Request',
                altText: 'Employee submits request',
                level: 2,
                sequence: 0,
                nodeType: WorkflowNodeType.localObjective,
                actorType: 'HUMAN',
              ),
              WorkflowTreeNode(
                id: 'lo_2',
                text: 'Review Request',
                altText: 'Manager reviews request',
                level: 2,
                sequence: 1,
                nodeType: WorkflowNodeType.localObjective,
                actorType: 'HUMAN',
              ),
              WorkflowTreeNode(
                id: 'lo_3',
                text: 'Approve Request',
                altText: 'System processes approval',
                level: 2,
                sequence: 2,
                nodeType: WorkflowNodeType.localObjective,
                actorType: 'SYSTEM',
                isTerminal: true,
              ),
            ],
          ),
        ],
      ),
    ];
  }
}
