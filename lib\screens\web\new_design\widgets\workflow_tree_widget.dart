import 'package:flutter/material.dart';
import '../../../../models/workflow_tree_node.dart';

class WorkflowTreeWidget extends StatefulWidget {
  final List<WorkflowTreeNode> nodes;
  final Function(WorkflowTreeNode)? onNodeTap;
  final Function(WorkflowTreeNode)? onNodeExpand;

  const WorkflowTreeWidget({
    Key? key,
    required this.nodes,
    this.onNodeTap,
    this.onNodeExpand,
  }) : super(key: key);

  @override
  State<WorkflowTreeWidget> createState() => _WorkflowTreeWidgetState();
}

class _WorkflowTreeWidgetState extends State<WorkflowTreeWidget> {
  Map<String, bool> expandedNodes = {};

  @override
  void initState() {
    super.initState();
    // Initialize expanded state for all nodes
    _initializeExpandedState(widget.nodes);
  }

  void _initializeExpandedState(List<WorkflowTreeNode> nodes) {
    for (var node in nodes) {
      expandedNodes[node.id] = node.isExpanded;
      if (node.children.isNotEmpty) {
        _initializeExpandedState(node.children);
      }
    }
  }

  void _toggleExpanded(WorkflowTreeNode node) {
    setState(() {
      expandedNodes[node.id] = !(expandedNodes[node.id] ?? false);
    });
    widget.onNodeExpand?.call(node);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, width: 1),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Text(
              'Workflow Tree Structure',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
            ),
          ),
          // Tree content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: widget.nodes.map((node) => _buildTreeNode(node, 0)).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTreeNode(WorkflowTreeNode node, int depth) {
    final isExpanded = expandedNodes[node.id] ?? false;
    final hasChildren = node.children.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Node content
        Container(
          margin: EdgeInsets.only(bottom: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Indentation and connecting lines
              _buildIndentation(depth, hasChildren, isExpanded, node),
              
              // Node icon and content
              Expanded(
                child: _buildNodeContent(node, hasChildren, isExpanded),
              ),
            ],
          ),
        ),
        
        // Children nodes
        if (hasChildren && isExpanded)
          ...node.children.map((child) => _buildTreeNode(child, depth + 1)).toList(),
      ],
    );
  }

  Widget _buildIndentation(int depth, bool hasChildren, bool isExpanded, WorkflowTreeNode node) {
    return Container(
      width: depth * 24.0 + (hasChildren ? 24.0 : 16.0),
      height: 24,
      child: Stack(
        children: [
          // Vertical connecting lines for parent levels
          for (int i = 0; i < depth; i++)
            Positioned(
              left: i * 24.0 + 8,
              top: 0,
              bottom: 0,
              child: Container(
                width: 1,
                color: Colors.grey.shade300,
              ),
            ),
          
          // Horizontal connecting line
          if (depth > 0)
            Positioned(
              left: (depth - 1) * 24.0 + 8,
              top: 12,
              width: hasChildren ? 16 : 24,
              height: 1,
              child: Container(color: Colors.grey.shade300),
            ),
          
          // Expand/collapse button
          if (hasChildren)
            Positioned(
              right: 0,
              top: 4,
              child: GestureDetector(
                onTap: () => _toggleExpanded(node),
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: Colors.grey.shade400),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Icon(
                    isExpanded ? Icons.remove : Icons.add,
                    size: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNodeContent(WorkflowTreeNode node, bool hasChildren, bool isExpanded) {
    return GestureDetector(
      onTap: () {
        if (hasChildren) {
          _toggleExpanded(node);
        }
        widget.onNodeTap?.call(node);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getNodeBackgroundColor(node),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: _getNodeBorderColor(node),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Node icon
            _buildNodeIcon(node),
            SizedBox(width: 8),
            
            // Node text
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    node.text,
                    style: TextStyle(
                      fontSize: _getNodeFontSize(node),
                      fontWeight: _getNodeFontWeight(node),
                      fontFamily: 'TiemposText',
                      color: _getNodeTextColor(node),
                    ),
                  ),
                  if (node.altText != null && node.altText!.isNotEmpty)
                    Text(
                      node.altText!,
                      style: TextStyle(
                        fontSize: 11,
                        fontFamily: 'TiemposText',
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ),
            ),
            
            // Status indicators
            _buildStatusIndicators(node),
          ],
        ),
      ),
    );
  }

  Widget _buildNodeIcon(WorkflowTreeNode node) {
    switch (node.nodeType) {
      case WorkflowNodeType.globalObjective:
        return Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.blue.shade600,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            Icons.flag,
            size: 12,
            color: Colors.white,
          ),
        );
      
      case WorkflowNodeType.pathway:
        return Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.green.shade600,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Center(
            child: Text(
              'P${node.sequence + 1}',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        );
      
      case WorkflowNodeType.localObjective:
        final isHuman = node.actorType == 'HUMAN';
        return Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: isHuman ? Colors.blue.shade500 : Colors.green.shade500,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Center(
            child: Text(
              isHuman ? 'H' : 'S',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        );
      
      case WorkflowNodeType.businessRule:
        return Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.orange.shade600,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Icon(
            Icons.rule,
            size: 12,
            color: Colors.white,
          ),
        );
      
      case WorkflowNodeType.parallel:
        return Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.purple.shade600,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Center(
            child: Text(
              '||',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        );
      
      default:
        return Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.grey.shade500,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            Icons.circle,
            size: 12,
            color: Colors.white,
          ),
        );
    }
  }

  Widget _buildStatusIndicators(WorkflowTreeNode node) {
    List<Widget> indicators = [];

    if (node.hasCheckmark) {
      indicators.add(
        Container(
          margin: EdgeInsets.only(left: 4),
          child: Icon(
            Icons.check_circle,
            size: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    if (node.hasX) {
      indicators.add(
        Container(
          margin: EdgeInsets.only(left: 4),
          child: Icon(
            Icons.cancel,
            size: 16,
            color: Colors.red.shade600,
          ),
        ),
      );
    }

    if (node.isTerminal) {
      indicators.add(
        Container(
          margin: EdgeInsets.only(left: 4),
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.red.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            'END',
            style: TextStyle(
              fontSize: 8,
              fontWeight: FontWeight.bold,
              color: Colors.red.shade700,
            ),
          ),
        ),
      );
    }

    if (node.isParallel) {
      indicators.add(
        Container(
          margin: EdgeInsets.only(left: 4),
          child: Icon(
            Icons.call_split,
            size: 16,
            color: Colors.purple.shade600,
          ),
        ),
      );
    }

    return Row(children: indicators);
  }

  Color _getNodeBackgroundColor(WorkflowTreeNode node) {
    switch (node.nodeType) {
      case WorkflowNodeType.globalObjective:
        return Colors.blue.shade50;
      case WorkflowNodeType.pathway:
        return Colors.green.shade50;
      case WorkflowNodeType.localObjective:
        return node.actorType == 'HUMAN' ? Colors.blue.shade50 : Colors.green.shade50;
      case WorkflowNodeType.businessRule:
        return Colors.orange.shade50;
      case WorkflowNodeType.parallel:
        return Colors.purple.shade50;
      default:
        return Colors.grey.shade50;
    }
  }

  Color _getNodeBorderColor(WorkflowTreeNode node) {
    switch (node.nodeType) {
      case WorkflowNodeType.globalObjective:
        return Colors.blue.shade200;
      case WorkflowNodeType.pathway:
        return Colors.green.shade200;
      case WorkflowNodeType.localObjective:
        return node.actorType == 'HUMAN' ? Colors.blue.shade200 : Colors.green.shade200;
      case WorkflowNodeType.businessRule:
        return Colors.orange.shade200;
      case WorkflowNodeType.parallel:
        return Colors.purple.shade200;
      default:
        return Colors.grey.shade200;
    }
  }

  Color _getNodeTextColor(WorkflowTreeNode node) {
    switch (node.nodeType) {
      case WorkflowNodeType.globalObjective:
        return Colors.blue.shade800;
      case WorkflowNodeType.pathway:
        return Colors.green.shade800;
      case WorkflowNodeType.localObjective:
        return node.actorType == 'HUMAN' ? Colors.blue.shade800 : Colors.green.shade800;
      case WorkflowNodeType.businessRule:
        return Colors.orange.shade800;
      case WorkflowNodeType.parallel:
        return Colors.purple.shade800;
      default:
        return Colors.black87;
    }
  }

  double _getNodeFontSize(WorkflowTreeNode node) {
    switch (node.nodeType) {
      case WorkflowNodeType.globalObjective:
        return 14;
      case WorkflowNodeType.pathway:
        return 13;
      case WorkflowNodeType.localObjective:
        return 12;
      default:
        return 11;
    }
  }

  FontWeight _getNodeFontWeight(WorkflowTreeNode node) {
    switch (node.nodeType) {
      case WorkflowNodeType.globalObjective:
        return FontWeight.w600;
      case WorkflowNodeType.pathway:
        return FontWeight.w500;
      default:
        return FontWeight.w400;
    }
  }
}
