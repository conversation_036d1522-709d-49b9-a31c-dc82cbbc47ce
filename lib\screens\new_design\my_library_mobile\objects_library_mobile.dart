import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';

import 'package:nsl/screens/new_design/my_library_mobile/books_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/solutions_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/mobile_nav_item.dart';

class ObjectMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final String versionNumber;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  ObjectMobile({
    required this.title,
    this.subtitle = '',
    required this.imageUrl,
    required this.versionNumber,
    this.isDraft = false,
    this.imageWidth = 107.0,
    this.imageHeight = 107.0,
  });

  factory ObjectMobile.fromJson(Map<String, dynamic> json) {
    return ObjectMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String? ?? '',
      imageUrl: json['imageUrl'] as String,
      versionNumber: json['versionNumber'] as String? ?? '',
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 107.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 107.0,
    );
  }
}

class ObjectsLibraryMobile extends StatefulWidget {
  const ObjectsLibraryMobile({super.key, this.showNavigationBar = true});

  final bool showNavigationBar;

  @override
  State<ObjectsLibraryMobile> createState() => _ObjectsLibraryMobileState();
}

class _ObjectsLibraryMobileState extends State<ObjectsLibraryMobile> {
  late List<ObjectMobile> objects;
  bool isLoading = true;
  int selectedTabIndex = 2; // 0: Books, 1: Solutions, 2: Objects

  // JSON string containing object data
  static const String objectsJsonString = '''
{
   "books": [
    {
      "title": "Customer Customer Customer",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Product Product Product",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Address Address Address",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals Home Rentals Home Rentals",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Online Grocery Online Grocery",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics Courier & Logistics Courier & Logistics",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Automotive Automotive Automotive",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness Fitness & Wellness Fitness & Wellness",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate Real Estate",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _loadObjects();
  }

  void _loadObjects() {
    try {
      // Parse the JSON string
      final data = json.decode(objectsJsonString);

      // Convert to ObjectMobile objects
      final List<ObjectMobile> loadedObjects = (data['books'] as List)
          .map((objectJson) => ObjectMobile.fromJson(objectJson))
          .toList();

      setState(() {
        objects = loadedObjects;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        objects = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading objects: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          widget.showNavigationBar ? Color(0xfff6f6f6) : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildObjectsGrid(),
        ],
      ),
      floatingActionButton: widget.showNavigationBar
          ? SizedBox(
              width: 46,
              height: 46,
              child: FloatingActionButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateBookMobile(),
                    ),
                  );
                },
                backgroundColor: const Color(0xff0058FF),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(Icons.add),
              ),
            )
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          // Hamburger menu icon
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          // Expanded widget to center the title
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('webobject.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // Invisible spacer to balance the layout (same width as menu icon)
          const SizedBox(width: 56), // IconButton default width
        ],
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MobileNavItem(
            iconPath: 'assets/images/books-icon.svg',
            label: AppLocalizations.of(context).translate('library.books'),
            isActive: selectedTabIndex == 0,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BooksLibraryMobile(),
                ),
              );
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/square-box-uncheck.svg',
            label: AppLocalizations.of(context).translate('library.solutions'),
            isActive: selectedTabIndex == 1,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SolutionsLibraryMobile(),
                ),
              );
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/cube-box.svg',
            label: AppLocalizations.of(context).translate('library.objects'),
            isActive: selectedTabIndex == 2,
            onTap: () {
              // Already on objects screen, no navigation needed
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Search bar
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                // Search text field
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0),
                    child: TextField(
                      decoration: InputDecoration(
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        hintText: 'Search',
                        border: InputBorder.none,
                        hintStyle:
                            TextStyle(fontSize: 14, color: Colors.grey[500]),
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ),
                // Search and filter icons
                _MobileObjectSvgButton(
                  iconPath: 'assets/images/search.svg',
                  onPressed: () {},
                  size: 20,
                ),
                Container(
                  height: 24,
                  width: 1,
                  color: Colors.grey.shade200,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                ),
                _MobileObjectSvgButton(
                  iconPath: 'assets/images/filter-icon.svg',
                  onPressed: () {},
                  size: 24,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // Create object button
          // SizedBox(
          //   width: double.infinity,
          //   height: 44,
          //   child: ElevatedButton(
          //     onPressed: () {},
          //     style: ElevatedButton.styleFrom(
          //       backgroundColor: const Color(0xff0058FF),
          //       foregroundColor: Colors.white,
          //       elevation: 0,
          //       shape: RoundedRectangleBorder(
          //         borderRadius: BorderRadius.circular(6),
          //       ),
          //     ),
          //     child: Text(
          //       AppLocalizations.of(context)
          //           .translate('webobject.createButtonText'),
          //       style: const TextStyle(
          //         fontSize: 16,
          //         fontWeight: FontWeight.w500,
          //         fontFamily: 'TiemposText',
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildObjectsGrid() {
    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Object grid
              isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : objects.isEmpty
                      ? const Center(child: Text('No objects found'))
                      : _buildMobileObjectGrid(),
              const SizedBox(height: 20),
              // Pagination
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _MobileObjectPaginationButton(
                    icon: const Icon(Icons.chevron_left, size: 20),
                    onPressed: () {
                      // Handle previous page
                    },
                  ),
                  const SizedBox(width: 8),
                  _MobileObjectPaginationButton(
                    icon: const Icon(Icons.chevron_right, size: 20),
                    onPressed: () {
                      // Handle next page
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileObjectGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine number of objects per row based on screen width
        int objectsPerRow = MediaQuery.of(context).size.width >= 600 ? 3 : 3;

        // Calculate rows needed
        int rowCount = (objects.length / objectsPerRow).ceil();

        // Calculate the width available for each object
        double availableWidth = constraints.maxWidth;
        double spacing = 16.0;
        double objectWidth =
            (availableWidth - (spacing * (objectsPerRow - 1))) / objectsPerRow;

        return Column(
          children: List.generate(rowCount, (rowIndex) {
            // Calculate start and end indices for this row
            int startIndex = rowIndex * objectsPerRow;
            int endIndex = (startIndex + objectsPerRow <= objects.length)
                ? startIndex + objectsPerRow
                : objects.length;

            // Create a list of objects for this row
            List<ObjectMobile> rowObjects =
                objects.sublist(startIndex, endIndex);

            return Padding(
              padding: const EdgeInsets.only(bottom: 24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: rowObjects.asMap().entries.map((entry) {
                  ObjectMobile object = entry.value;

                  return SizedBox(
                    width: objectWidth,
                    child: _MobileObjectCard(
                      onTap: () {
                        // Handle object tap
                      },
                      child: _buildMobileObjectCard(
                        title: object.title,
                        subtitle: object.subtitle,
                        imageUrl: object.imageUrl,
                        isDraft: object.isDraft,
                        imageWidth: objectWidth,
                        imageHeight: objectWidth, // Square aspect ratio (1:1)
                        versionNumber: object.versionNumber,
                        index: objects.indexOf(object),
                      ),
                    ),
                  );
                }).toList(),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildMobileObjectCard({
    required String title,
    String subtitle = '',
    required String versionNumber,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 107.0,
    double imageHeight = 107.0,
    int index = 0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Object cover
        Stack(
          children: [
            Container(
              width: imageWidth,
              height: imageHeight,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            if (isDraft)
              Positioned(
                top: imageHeight * 0.08, // Dynamic top position (8% from top)
                right:
                    imageWidth * 0.08, // Dynamic right position (8% from right)
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Draft',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: imageWidth,
          child: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              height: 1.334,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        // Version Number
        const SizedBox(height: 4),
        SizedBox(
          width: imageWidth,
          child: Text(
            versionNumber,
            style: const TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 11,
              fontFamily: "TiemposText",
              color: Colors.black,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

// Mobile Object SVG Button Widget
class _MobileObjectSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileObjectSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileObjectSvgButton> createState() => _MobileObjectSvgButtonState();
}

class _MobileObjectSvgButtonState extends State<_MobileObjectSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Object Pagination Button Widget
class _MobileObjectPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback onPressed;

  const _MobileObjectPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_MobileObjectPaginationButton> createState() =>
      _MobileObjectPaginationButtonState();
}

class _MobileObjectPaginationButtonState
    extends State<_MobileObjectPaginationButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.grey.shade300,
            width: 1.0,
          ),
          borderRadius: isPressed ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: Center(
          child: Icon(
            widget.icon.icon,
            color: isPressed ? const Color(0xff0058FF) : Colors.black,
            size: widget.icon.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Object Card Widget
class _MobileObjectCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _MobileObjectCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_MobileObjectCard> createState() => _MobileObjectCardState();
}

class _MobileObjectCardState extends State<_MobileObjectCard> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: widget.child,
    );
  }
}
