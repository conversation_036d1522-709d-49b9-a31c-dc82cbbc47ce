class TreeNavigationData {
  static List<Map<String, dynamic>> getTreeData() {
    return [
      {
        'id': 'product_catalog',
        'title': 'Product Catalog',
        'icon': 'assets/images/icons/product.png',
        'children': [
          {
            'id': 'cart',
            'title': 'Cart',
            'icon': 'assets/images/icons/cart.png',
            'children': []
          },
          {
            'id': 'products',
            'title': 'Products',
            'icon': 'assets/images/icons/product_item.png',
            'children': [
              {
                'id': 'categories',
                'title': 'Categories',
                'icon': 'assets/images/icons/category.png',
                'children': []
              },
              {
                'id': 'inventory',
                'title': 'Inventory',
                'icon': 'assets/images/icons/inventory.png',
                'children': []
              }
            ]
          },
        ]
      },
      {
        'id': 'crm',
        'title': 'CRM',
        'icon': 'assets/images/icons/crm.png',
        'children': [
          {
            'id': 'marketing_automation',
            'title': 'Marketing Automation',
            'icon': 'assets/images/icons/marketing.png',
            'children': [
              {
                'id': 'campaigns',
                'title': 'Campaigns',
                'icon': 'assets/images/icons/campaign.png',
                'children': []
              },
              {
                'id': 'email_templates',
                'title': 'Email Templates',
                'icon': 'assets/images/icons/email.png',
                'children': []
              }
            ]
          },
          {
            'id': 'customers',
            'title': 'Customers',
            'icon': 'assets/images/icons/customer.png',
            'children': []
          },
          {
            'id': 'leads',
            'title': 'Leads',
            'icon': 'assets/images/icons/lead.png',
            'children': []
          }
        ]
      },
      {
        'id': 'order_tracking',
        'title': 'Order Tracking',
        'icon': 'assets/images/icons/order.png',
        'children': [
          {
            'id': 'shipments',
            'title': 'Shipments',
            'icon': 'assets/images/icons/shipment.png',
            'children': []
          },
          {
            'id': 'returns',
            'title': 'Returns',
            'icon': 'assets/images/icons/return.png',
            'children': []
          }
        ]
      },
    ];
  }
}
