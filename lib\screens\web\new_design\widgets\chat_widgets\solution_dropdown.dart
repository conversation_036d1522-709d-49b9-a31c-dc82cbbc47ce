import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../../providers/web_home_provider.dart';

class SolutionDropdown extends StatefulWidget {
  final VoidCallback? onAIGeneratedSelected;
  final VoidCallback? onManualCreationSelected;
  final LayerLink? layerLink;
  final bool dropdownAbove;

  const SolutionDropdown({
    super.key,
    this.onAIGeneratedSelected,
    this.onManualCreationSelected,
    this.layerLink,
    this.dropdownAbove = false,
  });

  @override
  State<SolutionDropdown> createState() => _SolutionDropdownState();
}

class _SolutionDropdownState extends State<SolutionDropdown> {
  OverlayEntry? _overlayEntry;
  bool _isDropdownOpen = false;

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _isDropdownOpen = false;
  }

void _showOverlay() {
  if (_isDropdownOpen || widget.layerLink == null) return;

  _overlayEntry = OverlayEntry(
    builder: (context) => Stack(
      children: [
        // Detect tap outside
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            Provider.of<WebHomeProvider>(context, listen: false).clearQuickMessage();
          },
          child: Container(
            color: Colors.transparent,
          ),
        ),
        // Dropdown positioned
        Positioned(
          width: 200,
          child: CompositedTransformFollower(
            link: widget.layerLink!,
            showWhenUnlinked: false,
            offset: widget.dropdownAbove ? Offset(0, -120) : Offset(0, 40),
            child: Material(
              elevation: 4,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Color(0xffD0D0D0), width: 1),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildDropdownOption(
                      icon: Icons.auto_awesome,
                      text: 'AI Generated',
                      onTap: () {
                        _removeOverlay();
                        if (widget.onAIGeneratedSelected != null) {
                          widget.onAIGeneratedSelected!();
                        }
                      },
                    ),
                    SizedBox(height: 4),
                    _buildDropdownOption(
                      icon: Icons.edit,
                      text: 'Manual Creation',
                      onTap: () {
                        _removeOverlay();
                        if (widget.onManualCreationSelected != null) {
                          widget.onManualCreationSelected!();
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    ),
  );

  Overlay.of(context).insert(_overlayEntry!);
  _isDropdownOpen = true;
}
  
  
  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<WebHomeProvider>(context);

    // Show overlay when Solution is selected
    if (provider.selectedQuickMessage == 'Solution' && !_isDropdownOpen && widget.layerLink != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showOverlay();
      });
    } else if (provider.selectedQuickMessage != 'Solution' && _isDropdownOpen) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _removeOverlay();
      });
    }

    return SizedBox.shrink(); // This widget doesn't render anything directly
  }

  Widget _buildDropdownOption({
    required IconData icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: Colors.grey.shade600,
            ),
            SizedBox(width: 8),
            Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'TiemposText',
                color: Colors.black,
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
