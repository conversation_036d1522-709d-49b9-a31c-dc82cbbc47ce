import 'dart:math';
import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_alert_dialog.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../providers/build_provider.dart';
import '../models/message.dart';
import '../models/project_details.dart';
import '../widgets/navigation_drawer.dart';
import '../services/build_service.dart';
import '../services/speech_service.dart';
import '../widgets/chat_text_field.dart';
import '../theme/spacing.dart';
import '../widgets/responsive_builder.dart';
import '../widgets/full_width_user_bubble.dart';
import '../widgets/full_width_nsl_bubble.dart';
import '../widgets/nsl_logo.dart';
import '../utils/greeting_helper.dart';
import '../utils/logger.dart';
import '../widgets/project_details_form.dart';

class BuildScreen extends StatefulWidget {
  const BuildScreen({super.key});

  // Static method to toggle view
  static void toggleView(BuildContext context) {
    final BuildProvider buildProvider =
        Provider.of<BuildProvider>(context, listen: false);
    buildProvider.clearChat();

    // Find the nearest BuildScreen state and toggle its view
    final state = context.findRootAncestorStateOfType<_BuildScreenState>();
    if (state != null) {
      state._toggleView();
    } else {
      buildProvider.updateChatViewFlag(false);
    }
  }

  @override
  State<BuildScreen> createState() => _BuildScreenState();
}

class _BuildScreenState extends State<BuildScreen> {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Solution> _solutions = [];
  bool _isLoadingSolutions = false;
  final BuildService _buildService = BuildService();
  final SpeechService _speechService = SpeechService();

  // Pastel colors for card backgrounds
  final List<Color> _pastelColors = [
    const Color(0xFFFAEDCB), // Light yellow
    const Color(0xFFC9E4DE), // Mint green
    const Color(0xFFC6DEF1), // Light blue
    const Color(0xFFDBCDF0), // Lavender
    const Color(0xFFF2C6DE), // Pink
    const Color(0xFFF7D9C4), // Peach
  ];

  // Random generator for color selection
  final Random _random = Random();

  // Grid layout configuration

  @override
  void initState() {
    super.initState();
    Provider.of<BuildProvider>(context, listen: false).showChatView = false;
    _fetchSolutions();
    _initializeSpeechService();
  }

  Future<void> _initializeSpeechService() async {
    await _speechService.initialize();
    Logger.info('Speech service initialized in BuildScreen');
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _speechService.dispose();
    super.dispose();
  }

  Future<void> _fetchSolutions() async {
    setState(() {
      _isLoadingSolutions = true;
    });

    try {
      final solutions = await _buildService.getSolutions();
      setState(() {
        _solutions = solutions;
        _isLoadingSolutions = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingSolutions = false;
      });
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _toggleView() {
    final buildProvider = Provider.of<BuildProvider>(context, listen: false);

    // If switching to chat view, show project details form first
    if (!buildProvider.showChatView) {
      // Clear any existing chat and project details
      buildProvider.clearChat();
      buildProvider.cancelRequest();

      // Show project details form
      buildProvider.showProjectDetailsFormView();
      buildProvider.updateChatViewFlag(true);
    } else {
      // If switching back to solution list view
      buildProvider.cancelRequest();
      buildProvider.updateChatViewFlag(false);
    }
  }

  Future<void> _speakMessage(String message) async {
    // Check microphone permission first (needed for some devices)
    bool hasPermission = await _speechService.checkMicrophonePermission();
    if (!hasPermission) {
      // Request permission
      hasPermission = await _speechService.requestMicrophonePermission();
      if (!hasPermission) {
        // Show error message if permission denied
        _showPermissionDeniedDialog();
        return;
      }
    }

    Logger.info(
        'Speaking message: ${message.substring(0, message.length > 50 ? 50 : message.length)}...');
    await _speechService.speak(message);
  }

  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (context) => CustomAlertDialog(
        title: 'Microphone Permission Required',
        content:
            'This app needs microphone access for text-to-speech functionality. '
            'Please grant microphone permission in your device settings.',
        onClose: () => Navigator.of(context).pop(),
        primaryButtonText: 'OK',
        onPrimaryPressed: () {
          // Handle primary action
          Navigator.of(context).pop();
        },
        // secondaryButtonText: 'Cancel',
        // onSecondaryPressed: () => Navigator.of(context).pop(),
      ),
    );

    // showDialog(
    //   context: context,
    //   builder: (BuildContext context) {
    //     return AlertDialog(
    //       title: Text('Microphone Permission Required'),
    //       content: Text(
    //           'This app needs microphone access for text-to-speech functionality. '
    //           'Please grant microphone permission in your device settings.'),
    //       actions: [
    //         TextButton(
    //           onPressed: () => Navigator.of(context).pop(),
    //           child: Text('OK'),
    //         ),
    //       ],
    //     );
    //   },
    // );
  }

  // Helper method to determine device type based on screen width
  DeviceType getDeviceType(double width) {
    if (width < 600) {
      return DeviceType.mobile;
    } else if (width < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  Widget _buildInitialView() {
    // Get device type for responsive spacing

    // Get device type for responsive layout
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(
            AppSpacing.getResponsiveSpacing(AppSpacing.md, deviceType)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Create new solution button
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: _toggleView,
                  icon: Icon(
                    Icons.add,
                    // Use white icon for light theme, black for dark theme
                    color: Colors.white,
                  ),
                  label: Text(context.tr('build.createSolution')),
                  style: Theme.of(context).elevatedButtonTheme.style,
                ),
              ],
            ),
            SizedBox(height: AppSpacing.xl),

            // Previous solutions section
            // Text(
            //   'Previous Solutions',
            //   style: Theme.of(context).textTheme.headlineMedium,
            // ),
            SizedBox(height: AppSpacing.md),

            // Solutions grid
            _isLoadingSolutions
                ? Center(
                    child: CircularProgressIndicator(
                        color: Theme.of(context).colorScheme.primary))
                : _solutions.isEmpty
                    ? Center(
                        child: Text(
                          context.tr('build.noSolutions'),
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface
                                        .withAlpha(153),
                                  ),
                        ),
                      )
                    : GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: getDeviceType(
                                      MediaQuery.of(context).size.width) ==
                                  DeviceType.mobile
                              ? 2
                              : 3,
                          crossAxisSpacing: AppSpacing.xs,
                          mainAxisSpacing: AppSpacing.xs,
                          childAspectRatio: 0.85,
                        ),
                        itemCount: _solutions.length,
                        itemBuilder: (context, index) {
                          final solution = _solutions[index];
                          return _buildSolutionCard(solution);
                        },
                      ),
          ],
        ),
      ),
    );
  }

  Widget _buildSolutionCard(Solution solution) {
    // Get device type for responsive spacing
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);

    // Select a random pastel color for the card background
    final Color cardColor =
        _pastelColors[_random.nextInt(_pastelColors.length)];

    // Get the first user message as the title, or use a default
    String title = 'Solution';
    if (solution.chatHistory.isNotEmpty) {
      final firstUserMessage = solution.chatHistory.firstWhere(
        (msg) => msg.role == MessageRole.user,
        orElse: () => Message(
            role: MessageRole.user,
            content: 'Solution',
            timestamp: DateTime.now()),
      );
      title = firstUserMessage.content;
      // Truncate if too long
      if (title.length > 50) {
        title = '${title.substring(0, 47)}...';
      }
    } else if (solution.prescriptiveText.isNotEmpty) {
      title = '${solution.prescriptiveText.substring(0, 47)}...';
    }

    return Card(
      elevation: 1, // Use theme's card elevation
      shape: Theme.of(context).cardTheme.shape, // Use theme's card shape
      color: cardColor, // Apply the random pastel color
      child: InkWell(
        onTap: () {
          // Load this solution's chat history
          final buildProvider =
              Provider.of<BuildProvider>(context, listen: false);
          buildProvider.loadSolution(solution);
          buildProvider
              .hideProjectDetailsFormView(); // Ensure project details form is hidden
          buildProvider.updateChatViewFlag(true);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(
              AppSpacing.getResponsiveSpacing(AppSpacing.md, deviceType)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Version badge
              // Container(
              //   padding: EdgeInsets.symmetric(
              //     horizontal: AppSpacing.xs,
              //     vertical: AppSpacing.xxs,
              //   ),
              //   decoration: BoxDecoration(
              //     color: AppColors.primaryIndigo.withAlpha(30),
              //     borderRadius: BorderRadius.circular(8),
              //   ),
              //   child: Text(
              //     solution.yamlVersion,
              //     maxLines: 1,
              //     style: Theme.of(context).textTheme.titleSmall?.copyWith(
              //           color: AppColors.primaryIndigo,
              //           fontWeight: FontWeight.bold,
              //         ),
              //   ),
              // ),
              SizedBox(height: AppSpacing.sm),

              // Title
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const Spacer(),

              // View button
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    icon: const Icon(Icons.visibility, size: 16),
                    label: Text(context.tr('common.view')),
                    onPressed: () {
                      // Load this solution's chat history
                      final buildProvider =
                          Provider.of<BuildProvider>(context, listen: false);
                      buildProvider.loadSolution(solution);
                      buildProvider
                          .hideProjectDetailsFormView(); // Ensure project details form is hidden
                      buildProvider.updateChatViewFlag(true);
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSpacing.sm,
                        vertical: AppSpacing.xxs,
                      ),
                      textStyle: Theme.of(context).textTheme.titleSmall,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChatView() {
    // Get device type for responsive spacing
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);

    return Consumer<BuildProvider>(
      builder: (context, buildProvider, _) {
        // Show project details form if needed
        if (buildProvider.showProjectDetailsForm) {
          return Center(
            child: Container(
              constraints: BoxConstraints(maxWidth: 600),
              child: ProjectDetailsForm(
                onSubmit: (ProjectDetails details) {
                  // Save project details and continue to chat
                  buildProvider.setProjectDetails(details);
                  buildProvider.hideProjectDetailsFormView();
                },
              ),
            ),
          );
        }

        // Show chat view
        if (buildProvider.messages.isEmpty && !buildProvider.isLoading) {
          // Show greeting when no messages
          return Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // NSL Logo (matching the navigation drawer)
                  NSLLogo(
                    size: 100.0,
                    color: Theme.of(context)
                        .colorScheme
                        .primary, // Use theme primary color
                  ),
                  const SizedBox(height: 40),
                  if (buildProvider.projectDetails != null) ...[
                    Text(
                      "Project: ${buildProvider.projectDetails!.projectName}",
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    if (buildProvider.projectDetails!.description != null &&
                        buildProvider.projectDetails!.description!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24.0),
                        child: Text(
                          buildProvider.projectDetails!.description!,
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    const SizedBox(height: 24),
                  ],
                  Text(
                    context.tr('chat.greeting', args: {
                      'greeting': GreetingHelper.getTimeBasedGreeting()
                    }),
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                          fontSize: 28.0,
                          height: 1.3,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        // Show messages when conversation has started
        _scrollToBottom();
        return ListView.builder(
          controller: _scrollController,
          padding: EdgeInsets.symmetric(
            vertical:
                AppSpacing.getResponsiveSpacing(AppSpacing.sm, deviceType),
          ),
          itemCount:
              buildProvider.messages.length + (buildProvider.isLoading ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == buildProvider.messages.length) {
              return Container();
            }

            final message = buildProvider.messages[index];

            // Use FullWidthNSLBubble for NSL responses, FullWidthUserBubble for user messages
            if (message.role == MessageRole.assistant) {
              return FullWidthNSLBubble(
                message: message,
                response: index == buildProvider.messages.length - 1
                    ? buildProvider.lastResponse
                    : null,
                onLongPress: () => _speakMessage(message.content),
              );
            } else {
              return FullWidthUserBubble(message: message);
            }
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BuildProvider>(builder: (context, buildProvider, _) {
      return SafeArea(
        minimum: EdgeInsets.zero,
        child: Scaffold(
            drawer: const AppNavigationDrawer(currentRoute: 'create'),
            appBar: AppBar(
              title: buildProvider.showChatView
                  ? buildProvider.showProjectDetailsForm
                      ? Text(context.tr('build.newProject'))
                      : buildProvider.projectDetails != null
                          ? Text(buildProvider.projectDetails!.projectName)
                          : Text(context.tr('build.create'))
                  : Text(context.tr('build.create')),
              // Use theme's appBarTheme
              actions: buildProvider.showChatView
                  ? [
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: _toggleView,
                        tooltip: context.tr('build.backToCreateMenu'),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete_outline),
                        onPressed: () {
                          showDialog(
                            context: context,
                            builder: (context) => CustomAlertDialog(
                              title: 'build.clearChat',
                              content: 'build.clearChatConfirmation',
                              onClose: () => Navigator.of(context).pop(),
                              primaryButtonText: 'chat.cancel',
                              onPrimaryPressed: () {
                                // Handle primary action
                                Navigator.of(context).pop();
                              },
                              secondaryButtonText: 'chat.clear',
                              onSecondaryPressed: () {
                                Provider.of<BuildProvider>(context,
                                        listen: false)
                                    .clearChat();
                                Navigator.pop(context);
                              },
                            ),
                          );

                          //   showDialog(
                          //     context: context,
                          //     builder: (context) => AlertDialog(
                          //       title: Text(context.tr('build.clearChat'),
                          //           style:
                          //               Theme.of(context).textTheme.titleLarge),
                          //       content: Text(
                          //         context.tr('build.clearChatConfirmation'),
                          //         style: Theme.of(context).textTheme.bodyMedium,
                          //       ),
                          //       actions: [
                          //         TextButton(
                          //           onPressed: () => Navigator.pop(context),
                          //           child: Text(context.tr('chat.cancel'),
                          //               style: TextStyle(
                          //                   color: Theme.of(context)
                          //                       .colorScheme
                          //                       .onSurface
                          //                       .withAlpha(153))),
                          //         ),
                          //         TextButton(
                          //           onPressed: () {
                          //             Provider.of<BuildProvider>(context,
                          //                     listen: false)
                          //                 .clearChat();
                          //             Navigator.pop(context);
                          //           },
                          //           child: Text(context.tr('chat.clear'),
                          //               style: TextStyle(
                          //                   color: Theme.of(context)
                          //                       .colorScheme
                          //                       .error)),
                          //         ),
                          //       ],
                          //     ),
                          //   );
                        },
                      ),
                    ]
                  : [],
            ),
            body: Column(
              children: [
                buildProvider.showChatView
                    ? Expanded(child: _buildChatView())
                    : Expanded(child: _buildInitialView()),
                SizedBox(
                  height: AppSpacing.xxs,
                ),
                // Only show ChatTextField when not showing project details form
                if (!buildProvider.showProjectDetailsForm)
                  ChatTextField(
                    controller: _textController,
                    hintText: context.tr('chat.chatWithNSL'),
                    isLoading: buildProvider.isLoading,
                    onSubmitted: (value) {
                      buildProvider.sendMessage(value);
                    },
                    onCancel: () => buildProvider.cancelRequest(),
                  ),
              ],
            )),
      );
    });
  }
}
