{"nsl_file_name": "Leave Management Entity Definitions.nsl", "java_file_name": "LeaveManagementSystem.java", "prescriptives": 0, "sub_prescriptives": 0, "BETs": 0, "pathways": 0, "code": [{"line": 1, "code": "package com.leavemanagement.model;"}, {"line": 2, "code": ""}, {"line": 3, "code": "import java.time.LocalDate;"}, {"line": 4, "code": "import java.time.temporal.ChronoUnit;"}, {"line": 5, "code": "import java.time.format.DateTimeFormatter;"}, {"line": 6, "code": "import java.util.List;"}, {"line": 7, "code": "import java.util.ArrayList;"}, {"line": 8, "code": "import java.util.Map;"}, {"line": 9, "code": "import java.util.HashMap;"}, {"line": 10, "code": "import java.util.Optional;"}, {"line": 11, "code": "import java.sql.Connection;"}, {"line": 12, "code": "import java.sql.PreparedStatement;"}, {"line": 13, "code": "import java.sql.ResultSet;"}, {"line": 14, "code": "import java.sql.SQLException;"}, {"line": 15, "code": "import java.sql.Date;"}, {"line": 16, "code": "import java.util.UUID;"}, {"line": 17, "code": ""}, {"line": 18, "code": "public class LeaveApplication {"}, {"line": 19, "code": "    "}, {"line": 20, "code": "    private String leaveId;"}, {"line": 21, "code": "    private String employeeId;"}, {"line": 22, "code": "    private LocalDate startDate;"}, {"line": 23, "code": "    private LocalDate endDate;"}, {"line": 24, "code": "    private Integer numDays;"}, {"line": 25, "code": "    private String reason;"}, {"line": 26, "code": "    private LeaveStatus status;"}, {"line": 27, "code": "    private String remarks;"}, {"line": 28, "code": "    private String approvedBy;"}, {"line": 29, "code": "    private LeaveType leaveType;"}, {"line": 30, "code": "    private String leaveSubType;"}, {"line": 31, "code": "    "}, {"line": 32, "code": "    public LeaveApplication() {"}, {"line": 33, "code": "    }"}, {"line": 34, "code": "    "}, {"line": 35, "code": "    public LeaveApplication(String leaveId, String employeeId, LocalDate startDate, LocalDate endDate, "}, {"line": 36, "code": "                           Integer numDays, String reason, LeaveStatus status, String remarks, "}, {"line": 37, "code": "                           String approvedBy, LeaveType leaveType, String leaveSubType) {"}, {"line": 38, "code": "        this.leaveId = leaveId;"}, {"line": 39, "code": "        this.employeeId = employeeId;"}, {"line": 40, "code": "        this.startDate = startDate;"}, {"line": 41, "code": "        this.endDate = endDate;"}, {"line": 42, "code": "        this.numDays = numDays;"}, {"line": 43, "code": "        this.reason = reason;"}, {"line": 44, "code": "        this.status = status;"}, {"line": 45, "code": "        this.remarks = remarks;"}, {"line": 46, "code": "        this.approvedBy = approvedBy;"}, {"line": 47, "code": "        this.leaveType = leaveType;"}, {"line": 48, "code": "        this.leaveSubType = leaveSubType;"}, {"line": 49, "code": "    }"}, {"line": 50, "code": "    "}, {"line": 51, "code": "    public static LeaveApplicationBuilder builder() {"}, {"line": 52, "code": "        return new LeaveApplicationBuilder();"}, {"line": 53, "code": "    }"}, {"line": 54, "code": "    "}, {"line": 55, "code": "    public static class LeaveApplicationBuilder {"}, {"line": 56, "code": "        private String leaveId;"}, {"line": 57, "code": "        private String employeeId;"}, {"line": 58, "code": "        private LocalDate startDate;"}, {"line": 59, "code": "        private LocalDate endDate;"}, {"line": 60, "code": "        private Integer numDays;"}, {"line": 61, "code": "        private String reason;"}, {"line": 62, "code": "        private LeaveStatus status;"}, {"line": 63, "code": "        private String remarks;"}, {"line": 64, "code": "        private String approvedBy;"}, {"line": 65, "code": "        private LeaveType leaveType;"}, {"line": 66, "code": "        private String leaveSubType;"}, {"line": 67, "code": "        "}, {"line": 68, "code": "        public LeaveApplicationBuilder leaveId(String leaveId) {"}, {"line": 69, "code": "            this.leaveId = leaveId;"}, {"line": 70, "code": "            return this;"}, {"line": 71, "code": "        }"}, {"line": 72, "code": "        "}, {"line": 73, "code": "        public LeaveApplicationBuilder employeeId(String employeeId) {"}, {"line": 74, "code": "            this.employeeId = employeeId;"}, {"line": 75, "code": "            return this;"}, {"line": 76, "code": "        }"}, {"line": 77, "code": "        "}, {"line": 78, "code": "        public LeaveApplicationBuilder startDate(LocalDate startDate) {"}, {"line": 79, "code": "            this.startDate = startDate;"}, {"line": 80, "code": "            return this;"}, {"line": 81, "code": "        }"}, {"line": 82, "code": "        "}, {"line": 83, "code": "        public LeaveApplicationBuilder endDate(LocalDate endDate) {"}, {"line": 84, "code": "            this.endDate = endDate;"}, {"line": 85, "code": "            return this;"}, {"line": 86, "code": "        }"}, {"line": 87, "code": "        "}, {"line": 88, "code": "        public LeaveApplicationBuilder numDays(Integer numDays) {"}, {"line": 89, "code": "            this.numDays = numDays;"}, {"line": 90, "code": "            return this;"}, {"line": 91, "code": "        }"}, {"line": 92, "code": "        "}, {"line": 93, "code": "        public LeaveApplicationBuilder reason(String reason) {"}, {"line": 94, "code": "            this.reason = reason;"}, {"line": 95, "code": "            return this;"}, {"line": 96, "code": "        }"}, {"line": 97, "code": "        "}, {"line": 98, "code": "        public LeaveApplicationBuilder status(LeaveStatus status) {"}, {"line": 99, "code": "            this.status = status;"}, {"line": 100, "code": "            return this;"}, {"line": 101, "code": "        }"}, {"line": 102, "code": "        "}, {"line": 103, "code": "        public LeaveApplicationBuilder remarks(String remarks) {"}, {"line": 104, "code": "            this.remarks = remarks;"}, {"line": 105, "code": "            return this;"}, {"line": 106, "code": "        }"}, {"line": 107, "code": "        "}, {"line": 108, "code": "        public LeaveApplicationBuilder approvedBy(String approvedBy) {"}, {"line": 109, "code": "            this.approvedBy = approvedBy;"}, {"line": 110, "code": "            return this;"}, {"line": 111, "code": "        }"}, {"line": 112, "code": "        "}, {"line": 113, "code": "        public LeaveApplicationBuilder leaveType(LeaveType leaveType) {"}, {"line": 114, "code": "            this.leaveType = leaveType;"}, {"line": 115, "code": "            return this;"}, {"line": 116, "code": "        }"}, {"line": 117, "code": "        "}, {"line": 118, "code": "        public LeaveApplicationBuilder leaveSubType(String leaveSubType) {"}, {"line": 119, "code": "            this.leaveSubType = leaveSubType;"}, {"line": 120, "code": "            return this;"}, {"line": 121, "code": "        }"}, {"line": 122, "code": "        "}, {"line": 123, "code": "        public LeaveApplication build() {"}, {"line": 124, "code": "            return new LeaveApplication(leaveId, employeeId, startDate, endDate, numDays, reason, "}, {"line": 125, "code": "                                       status, remarks, approvedBy, leaveType, leaveSubType);"}, {"line": 126, "code": "        }"}, {"line": 127, "code": "    }"}, {"line": 128, "code": "    "}, {"line": 129, "code": "    public void calculateNumDays() {"}, {"line": 130, "code": "        if (startDate != null && endDate != null) {"}, {"line": 131, "code": "            numDays = (int) (ChronoUnit.DAYS.between(startDate, endDate) + 1);"}, {"line": 132, "code": "        }"}, {"line": 133, "code": "    }"}, {"line": 134, "code": "    "}, {"line": 135, "code": "    public void validate() throws ValidationException {"}, {"line": 136, "code": "        if (leaveId == null || leaveId.trim().isEmpty()) {"}, {"line": 137, "code": "            throw new ValidationException(\"Leave ID is required and must be unique\");"}, {"line": 138, "code": "        }"}, {"line": 139, "code": "        "}, {"line": 140, "code": "        if (employeeId == null || employeeId.trim().isEmpty()) {"}, {"line": 141, "code": "            throw new ValidationException(\"Employee ID is required\");"}, {"line": 142, "code": "        }"}, {"line": 143, "code": "        "}, {"line": 144, "code": "        if (startDate == null) {"}, {"line": 145, "code": "            throw new ValidationException(\"Start date is required\");"}, {"line": 146, "code": "        }"}, {"line": 147, "code": "        "}, {"line": 148, "code": "        if (endDate == null) {"}, {"line": 149, "code": "            throw new ValidationException(\"End date is required\");"}, {"line": 150, "code": "        }"}, {"line": 151, "code": "        "}, {"line": 152, "code": "        if (endDate.isBefore(startDate)) {"}, {"line": 153, "code": "            throw new ValidationException(\"End date must be after start date\");"}, {"line": 154, "code": "        }"}, {"line": 155, "code": "        "}, {"line": 156, "code": "        if (reason == null || reason.trim().length() < 10) {"}, {"line": 157, "code": "            throw new ValidationException(\"Please provide a detailed reason for your leave request\");"}, {"line": 158, "code": "        }"}, {"line": 159, "code": "        "}, {"line": 160, "code": "        if (leaveType == null) {"}, {"line": 161, "code": "            throw new ValidationException(\"Please select a valid leave type\");"}, {"line": 162, "code": "        }"}, {"line": 163, "code": "        "}, {"line": 164, "code": "        if (status == LeaveStatus.REJECTED && (remarks == null || remarks.trim().isEmpty())) {"}, {"line": 165, "code": "            throw new ValidationException(\"Please provide a reason for rejection\");"}, {"line": 166, "code": "        }"}, {"line": 167, "code": "    }"}, {"line": 168, "code": "    "}, {"line": 169, "code": "    public String getLeaveId() {"}, {"line": 170, "code": "        return leaveId;"}, {"line": 171, "code": "    }"}, {"line": 172, "code": "    "}, {"line": 173, "code": "    public void setLeaveId(String leaveId) {"}, {"line": 174, "code": "        this.leaveId = leaveId;"}, {"line": 175, "code": "    }"}, {"line": 176, "code": "    "}, {"line": 177, "code": "    public String getEmployeeId() {"}, {"line": 178, "code": "        return employeeId;"}, {"line": 179, "code": "    }"}, {"line": 180, "code": "    "}, {"line": 181, "code": "    public void setEmployeeId(String employeeId) {"}, {"line": 182, "code": "        this.employeeId = employeeId;"}, {"line": 183, "code": "    }"}, {"line": 184, "code": "    "}, {"line": 185, "code": "    public LocalDate getStartDate() {"}, {"line": 186, "code": "        return startDate;"}, {"line": 187, "code": "    }"}, {"line": 188, "code": "    "}, {"line": 189, "code": "    public void setStartDate(LocalDate startDate) {"}, {"line": 190, "code": "        this.startDate = startDate;"}, {"line": 191, "code": "    }"}, {"line": 192, "code": "    "}, {"line": 193, "code": "    public LocalDate getEndDate() {"}, {"line": 194, "code": "        return endDate;"}, {"line": 195, "code": "    }"}, {"line": 196, "code": "    "}, {"line": 197, "code": "    public void setEndDate(LocalDate endDate) {"}, {"line": 198, "code": "        this.endDate = endDate;"}, {"line": 199, "code": "    }"}, {"line": 200, "code": "    "}, {"line": 201, "code": "    public Integer getNumDays() {"}, {"line": 202, "code": "        return numDays;"}, {"line": 203, "code": "    }"}, {"line": 204, "code": "    "}, {"line": 205, "code": "    public void setNumDays(Integer numDays) {"}, {"line": 206, "code": "        this.numDays = numDays;"}, {"line": 207, "code": "    }"}, {"line": 208, "code": "    "}, {"line": 209, "code": "    public String getReason() {"}, {"line": 210, "code": "        return reason;"}, {"line": 211, "code": "    }"}, {"line": 212, "code": "    "}, {"line": 213, "code": "    public void setReason(String reason) {"}, {"line": 214, "code": "        this.reason = reason;"}, {"line": 215, "code": "    }"}, {"line": 216, "code": "    "}, {"line": 217, "code": "    public LeaveStatus getStatus() {"}, {"line": 218, "code": "        return status;"}, {"line": 219, "code": "    }"}, {"line": 220, "code": "    "}, {"line": 221, "code": "    public void setStatus(LeaveStatus status) {"}, {"line": 222, "code": "        this.status = status;"}, {"line": 223, "code": "    }"}, {"line": 224, "code": "    "}, {"line": 225, "code": "    public String getRemarks() {"}, {"line": 226, "code": "        return remarks;"}, {"line": 227, "code": "    }"}, {"line": 228, "code": "    "}, {"line": 229, "code": "    public void setRemarks(String remarks) {"}, {"line": 230, "code": "        this.remarks = remarks;"}, {"line": 231, "code": "    }"}, {"line": 232, "code": "    "}, {"line": 233, "code": "    public String getApprovedBy() {"}, {"line": 234, "code": "        return approvedBy;"}, {"line": 235, "code": "    }"}, {"line": 236, "code": "    "}, {"line": 237, "code": "    public void setApprovedBy(String approvedBy) {"}, {"line": 238, "code": "        this.approvedBy = approvedBy;"}, {"line": 239, "code": "    }"}, {"line": 240, "code": "    "}, {"line": 241, "code": "    public LeaveType getLeaveType() {"}, {"line": 242, "code": "        return leaveType;"}, {"line": 243, "code": "    }"}, {"line": 244, "code": "    "}, {"line": 245, "code": "    public void setLeaveType(LeaveType leaveType) {"}, {"line": 246, "code": "        this.leaveType = leaveType;"}, {"line": 247, "code": "    }"}, {"line": 248, "code": "    "}, {"line": 249, "code": "    public String getLeaveSubType() {"}, {"line": 250, "code": "        return leaveSubType;"}, {"line": 251, "code": "    }"}, {"line": 252, "code": "    "}, {"line": 253, "code": "    public void setLeaveSubType(String leaveSubType) {"}, {"line": 254, "code": "        this.leaveSubType = leaveSubType;"}, {"line": 255, "code": "    }"}, {"line": 256, "code": "}"}, {"line": 257, "code": ""}, {"line": 258, "code": "enum LeaveStatus {"}, {"line": 259, "code": "    PENDING(\"Pending\"),"}, {"line": 260, "code": "    APPROVED(\"Approved\"),"}, {"line": 261, "code": "    REJECTED(\"Rejected\");"}, {"line": 262, "code": "    "}, {"line": 263, "code": "    private final String displayName;"}, {"line": 264, "code": "    "}, {"line": 265, "code": "    LeaveStatus(String displayName) {"}, {"line": 266, "code": "        this.displayName = displayName;"}, {"line": 267, "code": "    }"}, {"line": 268, "code": "    "}, {"line": 269, "code": "    public String getDisplayName() {"}, {"line": 270, "code": "        return displayName;"}, {"line": 271, "code": "    }"}, {"line": 272, "code": "    "}, {"line": 273, "code": "    public static LeaveStatus fromDisplayName(String displayName) {"}, {"line": 274, "code": "        for (LeaveStatus status : values()) {"}, {"line": 275, "code": "            if (status.getDisplayName().equals(displayName)) {"}, {"line": 276, "code": "                return status;"}, {"line": 277, "code": "            }"}, {"line": 278, "code": "        }"}, {"line": 279, "code": "        throw new IllegalArgumentException(\"Unknown status: \" + displayName);"}, {"line": 280, "code": "    }"}, {"line": 281, "code": "}"}, {"line": 282, "code": ""}, {"line": 283, "code": "enum LeaveType {"}, {"line": 284, "code": "    ANNUAL_LEAVE(\"Annual Leave\"),"}, {"line": 285, "code": "    SICK_LEAVE(\"Sick Leave\"),"}, {"line": 286, "code": "    PARENTAL_LEAVE(\"Parental Leave\"),"}, {"line": 287, "code": "    BEREAVEMENT(\"Bereavement\");"}, {"line": 288, "code": "    "}, {"line": 289, "code": "    private final String displayName;"}, {"line": 290, "code": "    "}, {"line": 291, "code": "    LeaveType(String displayName) {"}, {"line": 292, "code": "        this.displayName = displayName;"}, {"line": 293, "code": "    }"}, {"line": 294, "code": "    "}, {"line": 295, "code": "    public String getDisplayName() {"}, {"line": 296, "code": "        return displayName;"}, {"line": 297, "code": "    }"}, {"line": 298, "code": "    "}, {"line": 299, "code": "    public static LeaveType fromDisplayName(String displayName) {"}, {"line": 300, "code": "        for (LeaveType type : values()) {"}, {"line": 301, "code": "            if (type.getDisplayName().equals(displayName)) {"}, {"line": 302, "code": "                return type;"}, {"line": 303, "code": "            }"}, {"line": 304, "code": "        }"}, {"line": 305, "code": "        throw new IllegalArgumentException(\"Unknown leave type: \" + displayName);"}, {"line": 306, "code": "    }"}, {"line": 307, "code": "}"}, {"line": 308, "code": ""}, {"line": 309, "code": "public class LeaveSubType {"}, {"line": 310, "code": "    "}, {"line": 311, "code": "    private String subTypeId;"}, {"line": 312, "code": "    private String leaveType;"}, {"line": 313, "code": "    private String subTypeName;"}, {"line": 314, "code": "    private Boolean active = true;"}, {"line": 315, "code": "    "}, {"line": 316, "code": "    public LeaveSubType() {"}, {"line": 317, "code": "    }"}, {"line": 318, "code": "    "}, {"line": 319, "code": "    public LeaveSubType(String subTypeId, String leaveType, String subTypeName, Boolean active) {"}, {"line": 320, "code": "        this.subTypeId = subTypeId;"}, {"line": 321, "code": "        this.leaveType = leaveType;"}, {"line": 322, "code": "        this.subTypeName = subTypeName;"}, {"line": 323, "code": "        this.active = active != null ? active : true;"}, {"line": 324, "code": "    }"}, {"line": 325, "code": "    "}, {"line": 326, "code": "    public static LeaveSubTypeBuilder builder() {"}, {"line": 327, "code": "        return new LeaveSubTypeBuilder();"}, {"line": 328, "code": "    }"}, {"line": 329, "code": "    "}, {"line": 330, "code": "    public static class LeaveSubTypeBuilder {"}, {"line": 331, "code": "        private String subTypeId;"}, {"line": 332, "code": "        private String leaveType;"}, {"line": 333, "code": "        private String subTypeName;"}, {"line": 334, "code": "        private Boolean active = true;"}, {"line": 335, "code": "        "}, {"line": 336, "code": "        public LeaveSubTypeBuilder subTypeId(String subTypeId) {"}, {"line": 337, "code": "            this.subTypeId = subTypeId;"}, {"line": 338, "code": "            return this;"}, {"line": 339, "code": "        }"}, {"line": 340, "code": "        "}, {"line": 341, "code": "        public LeaveSubTypeBuilder leaveType(String leaveType) {"}, {"line": 342, "code": "            this.leaveType = leaveType;"}, {"line": 343, "code": "            return this;"}, {"line": 344, "code": "        }"}, {"line": 345, "code": "        "}, {"line": 346, "code": "        public LeaveSubTypeBuilder subTypeName(String subTypeName) {"}, {"line": 347, "code": "            this.subTypeName = subTypeName;"}, {"line": 348, "code": "            return this;"}, {"line": 349, "code": "        }"}, {"line": 350, "code": "        "}, {"line": 351, "code": "        public LeaveSubTypeBuilder active(Boolean active) {"}, {"line": 352, "code": "            this.active = active;"}, {"line": 353, "code": "            return this;"}, {"line": 354, "code": "        }"}, {"line": 355, "code": "        "}, {"line": 356, "code": "        public LeaveSubType build() {"}, {"line": 357, "code": "            return new LeaveSubType(subTypeId, leaveType, subTypeName, active);"}, {"line": 358, "code": "        }"}, {"line": 359, "code": "    }"}, {"line": 360, "code": "    "}, {"line": 361, "code": "    public String getSubTypeId() {"}, {"line": 362, "code": "        return subTypeId;"}, {"line": 363, "code": "    }"}, {"line": 364, "code": "    "}, {"line": 365, "code": "    public void setSubTypeId(String subTypeId) {"}, {"line": 366, "code": "        this.subTypeId = subTypeId;"}, {"line": 367, "code": "    }"}, {"line": 368, "code": "    "}, {"line": 369, "code": "    public String getLeaveType() {"}, {"line": 370, "code": "        return leaveType;"}, {"line": 371, "code": "    }"}, {"line": 372, "code": "    "}, {"line": 373, "code": "    public void setLeaveType(String leaveType) {"}, {"line": 374, "code": "        this.leaveType = leaveType;"}, {"line": 375, "code": "    }"}, {"line": 376, "code": "    "}, {"line": 377, "code": "    public String getSubTypeName() {"}, {"line": 378, "code": "        return subTypeName;"}, {"line": 379, "code": "    }"}, {"line": 380, "code": "    "}, {"line": 381, "code": "    public void setSubTypeName(String subTypeName) {"}, {"line": 382, "code": "        this.subTypeName = subTypeName;"}, {"line": 383, "code": "    }"}, {"line": 384, "code": "    "}, {"line": 385, "code": "    public Boolean getActive() {"}, {"line": 386, "code": "        return active;"}, {"line": 387, "code": "    }"}, {"line": 388, "code": "    "}, {"line": 389, "code": "    public void setActive(Boolean active) {"}, {"line": 390, "code": "        this.active = active;"}, {"line": 391, "code": "    }"}, {"line": 392, "code": "}"}, {"line": 393, "code": ""}, {"line": 394, "code": "public class User {"}, {"line": 395, "code": "    "}, {"line": 396, "code": "    private String userId;"}, {"line": 397, "code": "    private String username;"}, {"line": 398, "code": "    private String email;"}, {"line": 399, "code": "    private String firstName;"}, {"line": 400, "code": "    private String lastName;"}, {"line": 401, "code": "    private UserStatus status = UserStatus.ACTIVE;"}, {"line": 402, "code": "    private String passwordHash;"}, {"line": 403, "code": "    private Boolean disabled = false;"}, {"line": 404, "code": "    private String organization;"}, {"line": 405, "code": "    private String team;"}, {"line": 406, "code": "    private List<Role> roles = new ArrayList<>();"}, {"line": 407, "code": "    "}, {"line": 408, "code": "    public User() {"}, {"line": 409, "code": "    }"}, {"line": 410, "code": "    "}, {"line": 411, "code": "    public User(String userId, String username, String email, String firstName, String lastName,"}, {"line": 412, "code": "               UserStatus status, String passwordHash, Boolean disabled, String organization, "}, {"line": 413, "code": "               String team, List<Role> roles) {"}, {"line": 414, "code": "        this.userId = userId;"}, {"line": 415, "code": "        this.username = username;"}, {"line": 416, "code": "        this.email = email;"}, {"line": 417, "code": "        this.firstName = firstName;"}, {"line": 418, "code": "        this.lastName = lastName;"}, {"line": 419, "code": "        this.status = status != null ? status : UserStatus.ACTIVE;"}, {"line": 420, "code": "        this.passwordHash = passwordHash;"}, {"line": 421, "code": "        this.disabled = disabled != null ? disabled : false;"}, {"line": 422, "code": "        this.organization = organization;"}, {"line": 423, "code": "        this.team = team;"}, {"line": 424, "code": "        this.roles = roles != null ? roles : new ArrayList<>();"}, {"line": 425, "code": "    }"}, {"line": 426, "code": "    "}, {"line": 427, "code": "    public static UserBuilder builder() {"}, {"line": 428, "code": "        return new UserBuilder();"}, {"line": 429, "code": "    }"}, {"line": 430, "code": "    "}, {"line": 431, "code": "    public static class UserBuilder {"}, {"line": 432, "code": "        private String userId;"}, {"line": 433, "code": "        private String username;"}, {"line": 434, "code": "        private String email;"}, {"line": 435, "code": "        private String firstName;"}, {"line": 436, "code": "        private String lastName;"}, {"line": 437, "code": "        private UserStatus status = UserStatus.ACTIVE;"}, {"line": 438, "code": "        private String passwordHash;"}, {"line": 439, "code": "        private Boolean disabled = false;"}, {"line": 440, "code": "        private String organization;"}, {"line": 441, "code": "        private String team;"}, {"line": 442, "code": "        private List<Role> roles = new ArrayList<>();"}, {"line": 443, "code": "        "}, {"line": 444, "code": "        public UserBuilder userId(String userId) {"}, {"line": 445, "code": "            this.userId = userId;"}, {"line": 446, "code": "            return this;"}, {"line": 447, "code": "        }"}, {"line": 448, "code": "        "}, {"line": 449, "code": "        public UserBuilder username(String username) {"}, {"line": 450, "code": "            this.username = username;"}, {"line": 451, "code": "            return this;"}, {"line": 452, "code": "        }"}, {"line": 453, "code": "        "}, {"line": 454, "code": "        public UserBuilder email(String email) {"}, {"line": 455, "code": "            this.email = email;"}, {"line": 456, "code": "            return this;"}, {"line": 457, "code": "        }"}, {"line": 458, "code": "        "}, {"line": 459, "code": "        public UserBuilder firstName(String firstName) {"}, {"line": 460, "code": "            this.firstName = firstName;"}, {"line": 461, "code": "            return this;"}, {"line": 462, "code": "        }"}, {"line": 463, "code": "        "}, {"line": 464, "code": "        public UserBuilder lastName(String lastName) {"}, {"line": 465, "code": "            this.lastName = lastName;"}, {"line": 466, "code": "            return this;"}, {"line": 467, "code": "        }"}, {"line": 468, "code": "        "}, {"line": 469, "code": "        public UserBuilder status(UserStatus status) {"}, {"line": 470, "code": "            this.status = status;"}, {"line": 471, "code": "            return this;"}, {"line": 472, "code": "        }"}, {"line": 473, "code": "        "}, {"line": 474, "code": "        public UserBuilder passwordHash(String passwordHash) {"}, {"line": 475, "code": "            this.passwordHash = passwordHash;"}, {"line": 476, "code": "            return this;"}, {"line": 477, "code": "        }"}, {"line": 478, "code": "        "}, {"line": 479, "code": "        public UserBuilder disabled(Boolean disabled) {"}, {"line": 480, "code": "            this.disabled = disabled;"}, {"line": 481, "code": "            return this;"}, {"line": 482, "code": "        }"}, {"line": 483, "code": "        "}, {"line": 484, "code": "        public UserBuilder organization(String organization) {"}, {"line": 485, "code": "            this.organization = organization;"}, {"line": 486, "code": "            return this;"}, {"line": 487, "code": "        }"}, {"line": 488, "code": "        "}, {"line": 489, "code": "        public UserBuilder team(String team) {"}, {"line": 490, "code": "            this.team = team;"}, {"line": 491, "code": "            return this;"}, {"line": 492, "code": "        }"}, {"line": 493, "code": "        "}, {"line": 494, "code": "        public UserBuilder roles(List<Role> roles) {"}, {"line": 495, "code": "            this.roles = roles;"}, {"line": 496, "code": "            return this;"}, {"line": 497, "code": "        }"}, {"line": 498, "code": "        "}, {"line": 499, "code": "        public User build() {"}, {"line": 500, "code": "            return new User(userId, username, email, firstName, lastName, status, passwordHash,"}, {"line": 501, "code": "                          disabled, organization, team, roles);"}, {"line": 502, "code": "        }"}, {"line": 503, "code": "    }"}, {"line": 504, "code": "    "}, {"line": 505, "code": "    public boolean hasRole(String roleName) {"}, {"line": 506, "code": "        return roles.stream()"}, {"line": 507, "code": "                .anyMatch(role -> role.getName().equals(roleName));"}, {"line": 508, "code": "    }"}, {"line": 509, "code": "    "}, {"line": 510, "code": "    public String getUserId() {"}, {"line": 511, "code": "        return userId;"}, {"line": 512, "code": "    }"}, {"line": 513, "code": "    "}, {"line": 514, "code": "    public void setUserId(String userId) {"}, {"line": 515, "code": "        this.userId = userId;"}, {"line": 516, "code": "    }"}, {"line": 517, "code": "    "}, {"line": 518, "code": "    public String getUsername() {"}, {"line": 519, "code": "        return username;"}, {"line": 520, "code": "    }"}, {"line": 521, "code": "    "}, {"line": 522, "code": "    public void setUsername(String username) {"}, {"line": 523, "code": "        this.username = username;"}, {"line": 524, "code": "    }"}, {"line": 525, "code": "    "}, {"line": 526, "code": "    public String getEmail() {"}, {"line": 527, "code": "        return email;"}, {"line": 528, "code": "    }"}, {"line": 529, "code": "    "}, {"line": 530, "code": "    public void setEmail(String email) {"}, {"line": 531, "code": "        this.email = email;"}, {"line": 532, "code": "    }"}, {"line": 533, "code": "    "}, {"line": 534, "code": "    public String getFirstName() {"}, {"line": 535, "code": "        return firstName;"}, {"line": 536, "code": "    }"}, {"line": 537, "code": "    "}, {"line": 538, "code": "    public void setFirstName(String firstName) {"}, {"line": 539, "code": "        this.firstName = firstName;"}, {"line": 540, "code": "    }"}, {"line": 541, "code": "    "}, {"line": 542, "code": "    public String getLastName() {"}, {"line": 543, "code": "        return lastName;"}, {"line": 544, "code": "    }"}, {"line": 545, "code": "    "}, {"line": 546, "code": "    public void setLastName(String lastName) {"}, {"line": 547, "code": "        this.lastName = lastName;"}, {"line": 548, "code": "    }"}, {"line": 549, "code": "    "}, {"line": 550, "code": "    public UserStatus getStatus() {"}, {"line": 551, "code": "        return status;"}, {"line": 552, "code": "    }"}, {"line": 553, "code": "    "}, {"line": 554, "code": "    public void setStatus(UserStatus status) {"}, {"line": 555, "code": "        this.status = status;"}, {"line": 556, "code": "    }"}, {"line": 557, "code": "    "}, {"line": 558, "code": "    public String getPasswordHash() {"}, {"line": 559, "code": "        return passwordHash;"}, {"line": 560, "code": "    }"}, {"line": 561, "code": "    "}, {"line": 562, "code": "    public void setPasswordHash(String passwordHash) {"}, {"line": 563, "code": "        this.passwordHash = passwordHash;"}, {"line": 564, "code": "    }"}, {"line": 565, "code": "    "}, {"line": 566, "code": "    public Boolean getDisabled() {"}, {"line": 567, "code": "        return disabled;"}, {"line": 568, "code": "    }"}, {"line": 569, "code": "    "}, {"line": 570, "code": "    public void setDisabled(Boolean disabled) {"}, {"line": 571, "code": "        this.disabled = disabled;"}, {"line": 572, "code": "    }"}, {"line": 573, "code": "    "}, {"line": 574, "code": "    public String getOrganization() {"}, {"line": 575, "code": "        return organization;"}, {"line": 576, "code": "    }"}, {"line": 577, "code": "    "}, {"line": 578, "code": "    public void setOrganization(String organization) {"}, {"line": 579, "code": "        this.organization = organization;"}, {"line": 580, "code": "    }"}, {"line": 581, "code": "    "}, {"line": 582, "code": "    public String getTeam() {"}, {"line": 583, "code": "        return team;"}, {"line": 584, "code": "    }"}, {"line": 585, "code": "    "}, {"line": 586, "code": "    public void setTeam(String team) {"}, {"line": 587, "code": "        this.team = team;"}, {"line": 588, "code": "    }"}, {"line": 589, "code": "    "}, {"line": 590, "code": "    public List<Role> getRoles() {"}, {"line": 591, "code": "        return roles;"}, {"line": 592, "code": "    }"}, {"line": 593, "code": "    "}, {"line": 594, "code": "    public void setRoles(List<Role> roles) {"}, {"line": 595, "code": "        this.roles = roles;"}, {"line": 596, "code": "    }"}, {"line": 597, "code": "}"}, {"line": 598, "code": ""}, {"line": 599, "code": "enum UserStatus {"}, {"line": 600, "code": "    ACTIVE(\"active\"),"}, {"line": 601, "code": "    INACTIVE(\"inactive\"),"}, {"line": 602, "code": "    SUSPENDED(\"suspended\");"}, {"line": 603, "code": "    "}, {"line": 604, "code": "    private final String displayName;"}, {"line": 605, "code": "    "}, {"line": 606, "code": "    UserStatus(String displayName) {"}, {"line": 607, "code": "        this.displayName = displayName;"}, {"line": 608, "code": "    }"}, {"line": 609, "code": "    "}, {"line": 610, "code": "    public String getDisplayName() {"}, {"line": 611, "code": "        return displayName;"}, {"line": 612, "code": "    }"}, {"line": 613, "code": "    "}, {"line": 614, "code": "    public static UserStatus fromDisplayName(String displayName) {"}, {"line": 615, "code": "        for (UserStatus status : values()) {"}, {"line": 616, "code": "            if (status.getDisplayName().equals(displayName)) {"}, {"line": 617, "code": "                return status;"}, {"line": 618, "code": "            }"}, {"line": 619, "code": "        }"}, {"line": 620, "code": "        throw new IllegalArgumentException(\"Unknown user status: \" + displayName);"}, {"line": 621, "code": "    }"}, {"line": 622, "code": "}"}, {"line": 623, "code": ""}, {"line": 624, "code": "public class Role {"}, {"line": 625, "code": "    "}, {"line": 626, "code": "    private String roleId;"}, {"line": 627, "code": "    private String name;"}, {"line": 628, "code": "    private String description;"}, {"line": 629, "code": "    private Role inheritsFrom;"}, {"line": 630, "code": "    private String tenantId;"}, {"line": 631, "code": "    "}, {"line": 632, "code": "    public Role() {"}, {"line": 633, "code": "    }"}, {"line": 634, "code": "    "}, {"line": 635, "code": "    public Role(String roleId, String name, String description, Role inheritsFrom, String tenantId) {"}, {"line": 636, "code": "        this.roleId = roleId;"}, {"line": 637, "code": "        this.name = name;"}, {"line": 638, "code": "        this.description = description;"}, {"line": 639, "code": "        this.inheritsFrom = inheritsFrom;"}, {"line": 640, "code": "        this.tenantId = tenantId;"}, {"line": 641, "code": "    }"}, {"line": 642, "code": "    "}, {"line": 643, "code": "    public static RoleBuilder builder() {"}, {"line": 644, "code": "        return new RoleBuilder();"}, {"line": 645, "code": "    }"}, {"line": 646, "code": "    "}, {"line": 647, "code": "    public static class RoleBuilder {"}, {"line": 648, "code": "        private String roleId;"}, {"line": 649, "code": "        private String name;"}, {"line": 650, "code": "        private String description;"}, {"line": 651, "code": "        private Role inheritsFrom;"}, {"line": 652, "code": "        private String tenantId;"}, {"line": 653, "code": "        "}, {"line": 654, "code": "        public RoleBuilder roleId(String roleId) {"}, {"line": 655, "code": "            this.roleId = roleId;"}, {"line": 656, "code": "            return this;"}, {"line": 657, "code": "        }"}, {"line": 658, "code": "        "}, {"line": 659, "code": "        public RoleBuilder name(String name) {"}, {"line": 660, "code": "            this.name = name;"}, {"line": 661, "code": "            return this;"}, {"line": 662, "code": "        }"}, {"line": 663, "code": "        "}, {"line": 664, "code": "        public RoleBuilder description(String description) {"}, {"line": 665, "code": "            this.description = description;"}, {"line": 666, "code": "            return this;"}, {"line": 667, "code": "        }"}, {"line": 668, "code": "        "}, {"line": 669, "code": "        public RoleBuilder inheritsFrom(Role inheritsFrom) {"}, {"line": 670, "code": "            this.inheritsFrom = inheritsFrom;"}, {"line": 671, "code": "            return this;"}, {"line": 672, "code": "        }"}, {"line": 673, "code": "        "}, {"line": 674, "code": "        public RoleBuilder tenantId(String tenantId) {"}, {"line": 675, "code": "            this.tenantId = tenantId;"}, {"line": 676, "code": "            return this;"}, {"line": 677, "code": "        }"}, {"line": 678, "code": "        "}, {"line": 679, "code": "        public Role build() {"}, {"line": 680, "code": "            return new Role(roleId, name, description, inheritsFrom, tenantId);"}, {"line": 681, "code": "        }"}, {"line": 682, "code": "    }"}, {"line": 683, "code": "    "}, {"line": 684, "code": "    public String getRoleId() {"}, {"line": 685, "code": "        return roleId;"}, {"line": 686, "code": "    }"}, {"line": 687, "code": "    "}, {"line": 688, "code": "    public void setRoleId(String roleId) {"}, {"line": 689, "code": "        this.roleId = roleId;"}, {"line": 690, "code": "    }"}, {"line": 691, "code": "    "}, {"line": 692, "code": "    public String getName() {"}, {"line": 693, "code": "        return name;"}, {"line": 694, "code": "    }"}, {"line": 695, "code": "    "}, {"line": 696, "code": "    public void setName(String name) {"}, {"line": 697, "code": "        this.name = name;"}, {"line": 698, "code": "    }"}, {"line": 699, "code": "    "}, {"line": 700, "code": "    public String getDescription() {"}, {"line": 701, "code": "        return description;"}, {"line": 702, "code": "    }"}, {"line": 703, "code": "    "}, {"line": 704, "code": "    public void setDescription(String description) {"}, {"line": 705, "code": "        this.description = description;"}, {"line": 706, "code": "    }"}, {"line": 707, "code": "    "}, {"line": 708, "code": "    public Role getInheritsFrom() {"}, {"line": 709, "code": "        return inheritsFrom;"}, {"line": 710, "code": "    }"}, {"line": 711, "code": "    "}, {"line": 712, "code": "    public void setInheritsFrom(Role inheritsFrom) {"}, {"line": 713, "code": "        this.inheritsFrom = inheritsFrom;"}, {"line": 714, "code": "    }"}, {"line": 715, "code": "    "}, {"line": 716, "code": "    public String getTenantId() {"}, {"line": 717, "code": "        return tenantId;"}, {"line": 718, "code": "    }"}, {"line": 719, "code": "    "}, {"line": 720, "code": "    public void setTenantId(String tenantId) {"}, {"line": 721, "code": "        this.tenantId = tenantId;"}, {"line": 722, "code": "    }"}, {"line": 723, "code": "}"}, {"line": 724, "code": ""}, {"line": 725, "code": "class ValidationException extends RuntimeException {"}, {"line": 726, "code": "    public ValidationException(String message) {"}, {"line": 727, "code": "        super(message);"}, {"line": 728, "code": "    }"}, {"line": 729, "code": "}"}, {"line": 730, "code": ""}, {"line": 731, "code": "package com.leavemanagement.dao;"}, {"line": 732, "code": ""}, {"line": 733, "code": "import com.leavemanagement.model.*;"}, {"line": 734, "code": "import com.leavemanagement.util.DatabaseConnection;"}, {"line": 735, "code": ""}, {"line": 736, "code": "import java.sql.*;"}, {"line": 737, "code": "import java.time.LocalDate;"}, {"line": 738, "code": "import java.util.ArrayList;"}, {"line": 739, "code": "import java.util.List;"}, {"line": 740, "code": "import java.util.Optional;"}, {"line": 741, "code": ""}, {"line": 742, "code": "public class LeaveApplicationDAO {"}, {"line": 743, "code": "    "}, {"line": 744, "code": "    public LeaveApplication save(LeaveApplication leaveApplication) throws SQLException {"}, {"line": 745, "code": "        String sql = \"INSERT INTO leave_application (leave_id, employee_id, start_date, end_date, \" +"}, {"line": 746, "code": "                    \"num_days, reason, status, remarks, approved_by, leave_type, leave_sub_type) \" +"}, {"line": 747, "code": "                    \"VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) \" +"}, {"line": 748, "code": "                    \"ON CONFLICT (leave_id) DO UPDATE SET \" +"}, {"line": 749, "code": "                    \"employee_id = EXCLUDED.employee_id, \" +"}, {"line": 750, "code": "                    \"start_date = EXCLUDED.start_date, \" +"}, {"line": 751, "code": "                    \"end_date = EXCLUDED.end_date, \" +"}, {"line": 752, "code": "                    \"num_days = EXCLUDED.num_days, \" +"}, {"line": 753, "code": "                    \"reason = EXCLUDED.reason, \" +"}, {"line": 754, "code": "                    \"status = EXCLUDED.status, \" +"}, {"line": 755, "code": "                    \"remarks = EXCLUDED.remarks, \" +"}, {"line": 756, "code": "                    \"approved_by = EXCLUDED.approved_by, \" +"}, {"line": 757, "code": "                    \"leave_type = EXCLUDED.leave_type, \" +"}, {"line": 758, "code": "                    \"leave_sub_type = EXCLUDED.leave_sub_type\";"}, {"line": 759, "code": "        "}, {"line": 760, "code": "        try (Connection conn = DatabaseConnection.getConnection();"}, {"line": 761, "code": "             PreparedStatement pstmt = conn.prepareStatement(sql)) {"}, {"line": 762, "code": "            "}, {"line": 763, "code": "            pstmt.setString(1, leaveApplication.getLeaveId());"}, {"line": 764, "code": "            pstmt.setString(2, leaveApplication.getEmployeeId());"}, {"line": 765, "code": "            pstmt.setDate(3, Date.valueOf(leaveApplication.getStartDate()));"}, {"line": 766, "code": "            pstmt.setDate(4, Date.valueOf(leaveApplication.getEndDate()));"}, {"line": 767, "code": "            pstmt.setInt(5, leaveApplication.getNumDays());"}, {"line": 768, "code": "            pstmt.setString(6, leaveApplication.getReason());"}, {"line": 769, "code": "            pstmt.setString(7, leaveApplication.getStatus().name());"}, {"line": 770, "code": "            pstmt.setString(8, leaveApplication.getRemarks());"}, {"line": 771, "code": "            pstmt.setString(9, leaveApplication.getApprovedBy());"}, {"line": 772, "code": "            pstmt.setString(10, leaveApplication.getLeaveType().name());"}, {"line": 773, "code": "            pstmt.setString(11, leaveApplication.getLeaveSubType());"}, {"line": 774, "code": "            "}, {"line": 775, "code": "            pstmt.executeUpdate();"}, {"line": 776, "code": "            return leaveApplication;"}, {"line": 777, "code": "        }"}, {"line": 778, "code": "    }"}, {"line": 779, "code": "    "}, {"line": 780, "code": "    public Optional<LeaveApplication> findById(String leaveId) throws SQLException {"}, {"line": 781, "code": "        String sql = \"SELECT * FROM leave_application WHERE leave_id = ?\";"}, {"line": 782, "code": "        "}, {"line": 783, "code": "        try (Connection conn = DatabaseConnection.getConnection();"}, {"line": 784, "code": "             PreparedStatement pstmt = conn.prepareStatement(sql)) {"}, {"line": 785, "code": "            "}, {"line": 786, "code": "            pstmt.setString(1, leaveId);"}, {"line": 787, "code": "            "}, {"line": 788, "code": "            try (ResultSet rs = pstmt.executeQuery()) {"}, {"line": 789, "code": "                if (rs.next()) {"}, {"line": 790, "code": "                    LeaveApplication leaveApplication = new LeaveApplication();"}, {"line": 791, "code": "                    leaveApplication.setLeaveId(rs.getString(\"leave_id\"));"}, {"line": 792, "code": "                    leaveApplication.setEmployeeId(rs.getString(\"employee_id\"));"}, {"line": 793, "code": "                    leaveApplication.setStartDate(rs.getDate(\"start_date\").toLocalDate());"}, {"line": 794, "code": "                    leaveApplication.setEndDate(rs.getDate(\"end_date\").toLocalDate());"}, {"line": 795, "code": "                    leaveApplication.setNumDays(rs.getInt(\"num_days\"));"}, {"line": 796, "code": "                    leaveApplication.setReason(rs.getString(\"reason\"));"}, {"line": 797, "code": "                    leaveApplication.setStatus(LeaveStatus.valueOf(rs.getString(\"status\")));"}, {"line": 798, "code": "                    leaveApplication.setRemarks(rs.getString(\"remarks\"));"}, {"line": 799, "code": "                    leaveApplication.setApprovedBy(rs.getString(\"approved_by\"));"}, {"line": 800, "code": "                    leaveApplication.setLeaveType(LeaveType.valueOf(rs.getString(\"leave_type\")));"}, {"line": 801, "code": "                    leaveApplication.setLeaveSubType(rs.getString(\"leave_sub_type\"));"}, {"line": 802, "code": "                    "}, {"line": 803, "code": "                    return Optional.of(leaveApplication);"}, {"line": 804, "code": "                }"}, {"line": 805, "code": "                return Optional.empty();"}, {"line": 806, "code": "            }"}, {"line": 807, "code": "        }"}, {"line": 808, "code": "    }"}, {"line": 809, "code": "    "}, {"line": 810, "code": "    public List<LeaveApplication> findByEmployeeId(String employeeId) throws SQLException {"}, {"line": 811, "code": "        String sql = \"SELECT * FROM leave_application WHERE employee_id = ?\";"}, {"line": 812, "code": "        "}, {"line": 813, "code": "        try (Connection conn = DatabaseConnection.getConnection();"}, {"line": 814, "code": "             PreparedStatement pstmt = conn.prepareStatement(sql)) {"}, {"line": 815, "code": "            "}, {"line": 816, "code": "            pstmt.setString(1, employeeId);"}, {"line": 817, "code": "            "}, {"line": 818, "code": "            try (ResultSet rs = pstmt.executeQuery()) {"}, {"line": 819, "code": "                List<LeaveApplication> leaveApplications = new ArrayList<>();"}, {"line": 820, "code": "                "}, {"line": 821, "code": "                while (rs.next()) {"}, {"line": 822, "code": "                    LeaveApplication leaveApplication = new LeaveApplication();"}, {"line": 823, "code": "                    leaveApplication.setLeaveId(rs.getString(\"leave_id\"));"}, {"line": 824, "code": "                    leaveApplication.setEmployeeId(rs.getString(\"employee_id\"));"}, {"line": 825, "code": "                    leaveApplication.setStartDate(rs.getDate(\"start_date\").toLocalDate());"}, {"line": 826, "code": "                    leaveApplication.setEndDate(rs.getDate(\"end_date\").toLocalDate());"}, {"line": 827, "code": "                    leaveApplication.setNumDays(rs.getInt(\"num_days\"));"}, {"line": 828, "code": "                    leaveApplication.setReason(rs.getString(\"reason\"));"}, {"line": 829, "code": "                    leaveApplication.setStatus(LeaveStatus.valueOf(rs.getString(\"status\")));"}, {"line": 830, "code": "                    leaveApplication.setRemarks(rs.getString(\"remarks\"));"}, {"line": 831, "code": "                    leaveApplication.setApprovedBy(rs.getString(\"approved_by\"));"}, {"line": 832, "code": "                    leaveApplication.setLeaveType(LeaveType.valueOf(rs.getString(\"leave_type\")));"}, {"line": 833, "code": "                    leaveApplication.setLeaveSubType(rs.getString(\"leave_sub_type\"));"}, {"line": 834, "code": "                    "}, {"line": 835, "code": "                    leaveApplications.add(leaveApplication);"}, {"line": 836, "code": "                }"}, {"line": 837, "code": "                "}, {"line": 838, "code": "                return leaveApplications;"}, {"line": 839, "code": "            }"}, {"line": 840, "code": "        }"}, {"line": 841, "code": "    }"}, {"line": 842, "code": "    "}, {"line": 843, "code": "    public List<LeaveApplication> findByStatus(LeaveStatus status) throws SQLException {"}, {"line": 844, "code": "        String sql = \"SELECT * FROM leave_application WHERE status = ?\";"}, {"line": 845, "code": "        "}, {"line": 846, "code": "        try (Connection conn = DatabaseConnection.getConnection();"}, {"line": 847, "code": "             PreparedStatement pstmt = conn.prepareStatement(sql)) {"}, {"line": 848, "code": "            "}, {"line": 849, "code": "            pstmt.setString(1, status.name());"}, {"line": 850, "code": "            "}, {"line": 851, "code": "            try (ResultSet rs = pstmt.executeQuery()) {"}, {"line": 852, "code": "                List<LeaveApplication> leaveApplications = new ArrayList<>();"}, {"line": 853, "code": "                "}, {"line": 854, "code": "                while (rs.next()) {"}, {"line": 855, "code": "                    LeaveApplication leaveApplication = new LeaveApplication();"}, {"line": 856, "code": "                    leaveApplication.setLeaveId(rs.getString(\"leave_id\"));"}, {"line": 857, "code": "                    leaveApplication.setEmployeeId(rs.getString(\"employee_id\"));"}, {"line": 858, "code": "                    leaveApplication.setStartDate(rs.getDate(\"start_date\").toLocalDate());"}, {"line": 859, "code": "                    leaveApplication.setEndDate(rs.getDate(\"end_date\").toLocalDate());"}, {"line": 860, "code": "                    leaveApplication.setNumDays(rs.getInt(\"num_days\"));"}, {"line": 861, "code": "                    leaveApplication.setReason(rs.getString(\"reason\"));"}, {"line": 862, "code": "                    leaveApplication.setStatus(LeaveStatus.valueOf(rs.getString(\"status\")));"}, {"line": 863, "code": "                    leaveApplication.setRemarks(rs.getString(\"remarks\"));"}, {"line": 864, "code": "                    leaveApplication.setApprovedBy(rs.getString(\"approved_by\"));"}, {"line": 865, "code": "                    leaveApplication.setLeaveType(LeaveType.valueOf(rs.getString(\"leave_type\")));"}, {"line": 866, "code": "                    leaveApplication.setLeaveSubType(rs.getString(\"leave_sub_type\"));"}, {"line": 867, "code": "                    "}, {"line": 868, "code": "                    leaveApplications.add(leaveApplication);"}, {"line": 869, "code": "                }"}, {"line": 870, "code": "                "}, {"line": 871, "code": "                return leaveApplications;"}, {"line": 872, "code": "            }"}, {"line": 873, "code": "        }"}, {"line": 874, "code": "    }"}, {"line": 875, "code": "}"}, {"line": 876, "code": ""}, {"line": 877, "code": "public class LeaveSubTypeDAO {"}, {"line": 878, "code": "    "}, {"line": 879, "code": "    public List<LeaveSubType> findByLeaveTypeAndActiveTrue(String leaveType) throws SQLException {"}, {"line": 880, "code": "        String sql = \"SELECT * FROM leave_sub_type WHERE leave_type = ? AND active = true\";"}, {"line": 881, "code": "        "}, {"line": 882, "code": "        try (Connection conn = DatabaseConnection.getConnection();"}, {"line": 883, "code": "             PreparedStatement pstmt = conn.prepareStatement(sql)) {"}, {"line": 884, "code": "            "}, {"line": 885, "code": "            pstmt.setString(1, leaveType);"}, {"line": 886, "code": "            "}, {"line": 887, "code": "            try (ResultSet rs = pstmt.executeQuery()) {"}, {"line": 888, "code": "                List<LeaveSubType> leaveSubTypes = new ArrayList<>();"}, {"line": 889, "code": "                "}, {"line": 890, "code": "                while (rs.next()) {"}, {"line": 891, "code": "                    LeaveSubType leaveSubType = new LeaveSubType();"}, {"line": 892, "code": "                    leaveSubType.setSubTypeId(rs.getString(\"sub_type_id\"));"}, {"line": 893, "code": "                    leaveSubType.setLeaveType(rs.getString(\"leave_type\"));"}, {"line": 894, "code": "                    leaveSubType.setSubTypeName(rs.getString(\"sub_type_name\"));"}, {"line": 895, "code": "                    leaveSubType.setActive(rs.getBoolean(\"active\"));"}, {"line": 896, "code": "                    "}, {"line": 897, "code": "                    leaveSubTypes.add(leaveSubType);"}, {"line": 898, "code": "                }"}, {"line": 899, "code": "                "}, {"line": 900, "code": "                return leaveSubTypes;"}, {"line": 901, "code": "            }"}, {"line": 902, "code": "        }"}, {"line": 903, "code": "    }"}, {"line": 904, "code": "}"}, {"line": 905, "code": ""}, {"line": 906, "code": "public class UserDAO {"}, {"line": 907, "code": "    "}, {"line": 908, "code": "    public Optional<User> findByUsername(String username) throws SQLException {"}, {"line": 909, "code": "        String sql = \"SELECT * FROM user WHERE username = ?\";"}, {"line": 910, "code": "        "}, {"line": 911, "code": "        try (Connection conn = DatabaseConnection.getConnection();"}, {"line": 912, "code": "             PreparedStatement pstmt = conn.prepareStatement(sql)) {"}, {"line": 913, "code": "            "}, {"line": 914, "code": "            pstmt.setString(1, username);"}, {"line": 915, "code": "            "}, {"line": 916, "code": "            try (ResultSet rs = pstmt.executeQuery()) {"}, {"line": 917, "code": "                if (rs.next()) {"}, {"line": 918, "code": "                    User user = new User();"}, {"line": 919, "code": "                    user.setUserId(rs.getString(\"user_id\"));"}, {"line": 920, "code": "                    user.setUsername(rs.getString(\"username\"));"}, {"line": 921, "code": "                    user.setEmail(rs.getString(\"email\"));"}, {"line": 922, "code": "                    user.setFirstName(rs.getString(\"first_name\"));"}, {"line": 923, "code": "                    user.setLastName(rs.getString(\"last_name\"));"}, {"line": 924, "code": "                    user.setStatus(UserStatus.valueOf(rs.getString(\"status\")));"}, {"line": 925, "code": "                    user.setPasswordHash(rs.getString(\"password_hash\"));"}, {"line": 926, "code": "                    user.setDisabled(rs.getBoolean(\"disabled\"));"}, {"line": 927, "code": "                    user.setOrganization(rs.getString(\"organization\"));"}, {"line": 928, "code": "                    user.setTeam(rs.getString(\"team\"));"}, {"line": 929, "code": "                    "}, {"line": 930, "code": "                    user.setRoles(loadUserRoles(user.getUserId()));"}, {"line": 931, "code": "                    "}, {"line": 932, "code": "                    return Optional.of(user);"}, {"line": 933, "code": "                }"}, {"line": 934, "code": "                return Optional.empty();"}, {"line": 935, "code": "            }"}, {"line": 936, "code": "        }"}, {"line": 937, "code": "    }"}, {"line": 938, "code": "    "}, {"line": 939, "code": "    private List<Role> loadUserRoles(String userId) throws SQLException {"}, {"line": 940, "code": "        String sql = \"SELECT r.* FROM role r \" +"}, {"line": 941, "code": "                    \"JOIN user_role ur ON r.role_id = ur.role_id \" +"}, {"line": 942, "code": "                    \"WHERE ur.user_id = ?\";"}, {"line": 943, "code": "        "}, {"line": 944, "code": "        try (Connection conn = DatabaseConnection.getConnection();"}, {"line": 945, "code": "             PreparedStatement pstmt = conn.prepareStatement(sql)) {"}, {"line": 946, "code": "            "}, {"line": 947, "code": "            pstmt.setString(1, userId);"}, {"line": 948, "code": "            "}, {"line": 949, "code": "            try (ResultSet rs = pstmt.executeQuery()) {"}, {"line": 950, "code": "                List<Role> roles = new ArrayList<>();"}, {"line": 951, "code": "                "}, {"line": 952, "code": "                while (rs.next()) {"}, {"line": 953, "code": "                    Role role = new Role();"}, {"line": 954, "code": "                    role.setRoleId(rs.getString(\"role_id\"));"}, {"line": 955, "code": "                    role.setName(rs.getString(\"name\"));"}, {"line": 956, "code": "                    role.setDescription(rs.getString(\"description\"));"}, {"line": 957, "code": "                    role.setTenantId(rs.getString(\"tenant_id\"));"}, {"line": 958, "code": "                    "}, {"line": 959, "code": "                    String inheritsFromId = rs.getString(\"inherits_from\");"}, {"line": 960, "code": "                    if (inheritsFromId != null) {"}, {"line": 961, "code": "                        Role parentRole = new Role();"}, {"line": 962, "code": "                        parentRole.setRoleId(inheritsFromId);"}, {"line": 963, "code": "                        role.setInheritsFrom(parentRole);"}, {"line": 964, "code": "                    }"}, {"line": 965, "code": "                    "}, {"line": 966, "code": "                    roles.add(role);"}, {"line": 967, "code": "                }"}, {"line": 968, "code": "                "}, {"line": 969, "code": "                return roles;"}, {"line": 970, "code": "            }"}, {"line": 971, "code": "        }"}, {"line": 972, "code": "    }"}, {"line": 973, "code": "}"}, {"line": 974, "code": ""}, {"line": 975, "code": "package com.leavemanagement.service;"}, {"line": 976, "code": ""}, {"line": 977, "code": "import com.leavemanagement.model.*;"}, {"line": 978, "code": "import com.leavemanagement.dao.*;"}, {"line": 979, "code": ""}, {"line": 980, "code": "import java.time.LocalDate;"}, {"line": 981, "code": "import java.time.format.DateTimeFormatter;"}, {"line": 982, "code": "import java.util.List;"}, {"line": 983, "code": "import java.util.Optional;"}, {"line": 984, "code": "import java.sql.SQLException;"}, {"line": 985, "code": ""}, {"line": 986, "code": "public interface LeaveApplicationService {"}, {"line": 987, "code": "    LeaveApplication applyFor<PERSON><PERSON>ve(LeaveApplication leaveApplication) throws SQLException;"}, {"line": 988, "code": "    "}, {"line": 989, "code": "    LeaveApplication managerApproval(String leaveId, LeaveStatus status, String remarks, String approvedBy) "}, {"line": 990, "code": "        throws SQLException;"}, {"line": 991, "code": "    "}, {"line": 992, "code": "    LeaveApplication hrManagerApproval(String leaveId, LeaveStatus status, String remarks, String approvedBy) "}, {"line": 993, "code": "        throws SQLException;"}, {"line": 994, "code": "    "}, {"line": 995, "code": "    LeaveApplication getLeaveApplicationById(String leaveId) throws SQLException;"}, {"line": 996, "code": "    "}, {"line": 997, "code": "    List<LeaveSubType> getLeaveSubTypesByLeaveType(String leaveType) throws SQLException;"}, {"line": 998, "code": "    "}, {"line": 999, "code": "    String generateLeaveId();"}, {"line": 1000, "code": "}"}, {"line": 1001, "code": ""}, {"line": 1002, "code": "public class LeaveApplicationServiceImpl implements LeaveApplicationService {"}, {"line": 1003, "code": "    "}, {"line": 1004, "code": "    private LeaveApplicationDAO leaveApplicationDAO;"}, {"line": 1005, "code": "    private LeaveSubTypeDAO leaveSubTypeDAO;"}, {"line": 1006, "code": "    "}, {"line": 1007, "code": "    public LeaveApplicationServiceImpl() {"}, {"line": 1008, "code": "        this.leaveApplicationDAO = new LeaveApplicationDAO();"}, {"line": 1009, "code": "        this.leaveSubTypeDAO = new LeaveSubTypeDAO();"}, {"line": 1010, "code": "    }"}, {"line": 1011, "code": "    "}, {"line": 1012, "code": "    @Override"}, {"line": 1013, "code": "    public LeaveApplication applyFor<PERSON>eave(LeaveApplication leaveApplication) throws SQLException {"}, {"line": 1014, "code": "        if (leaveApplication.getLeaveId() == null || leaveApplication.getLeaveId().isEmpty()) {"}, {"line": 1015, "code": "            leaveApplication.setLeaveId(generateLeaveId());"}, {"line": 1016, "code": "        }"}, {"line": 1017, "code": "        "}, {"line": 1018, "code": "        leaveApplication.calculateNumDays();"}, {"line": 1019, "code": "        "}, {"line": 1020, "code": "        leaveApplication.setStatus(LeaveStatus.PENDING);"}, {"line": 1021, "code": "        "}, {"line": 1022, "code": "        leaveApplication.validate();"}, {"line": 1023, "code": "        "}, {"line": 1024, "code": "        return leaveApplicationDAO.save(leaveApplication);"}, {"line": 1025, "code": "    }"}, {"line": 1026, "code": "    "}, {"line": 1027, "code": "    @Override"}, {"line": 1028, "code": "    public LeaveApplication managerApproval(String leaveId, LeaveStatus status, String remarks, String approvedBy) "}, {"line": 1029, "code": "            throws SQLException {"}, {"line": 1030, "code": "        LeaveApplication leaveApplication = getLeaveApplicationById(leaveId);"}, {"line": 1031, "code": "        "}, {"line": 1032, "code": "        if (leaveApplication == null) {"}, {"line": 1033, "code": "            throw new ValidationException(\"Invalid leave application reference\");"}, {"line": 1034, "code": "        }"}, {"line": 1035, "code": "        "}, {"line": 1036, "code": "        if (leaveApplication.getNumDays() > 3) {"}, {"line": 1037, "code": "            throw new ValidationException(\"This leave application should be processed by HR Manager, not a regular Manager\");"}, {"line": 1038, "code": "        }"}, {"line": 1039, "code": "        "}, {"line": 1040, "code": "        leaveApplication.setStatus(status);"}, {"line": 1041, "code": "        leaveApplication.setRemarks(remarks);"}, {"line": 1042, "code": "        leaveApplication.setApprovedBy(approvedBy);"}, {"line": 1043, "code": "        "}, {"line": 1044, "code": "        leaveApplication.validate();"}, {"line": 1045, "code": "        "}, {"line": 1046, "code": "        return leaveApplicationDAO.save(leaveApplication);"}, {"line": 1047, "code": "    }"}, {"line": 1048, "code": "    "}, {"line": 1049, "code": "    @Override"}, {"line": 1050, "code": "    public LeaveApplication hrManagerApproval(String leaveId, LeaveStatus status, String remarks, String approvedBy) "}, {"line": 1051, "code": "            throws SQLException {"}, {"line": 1052, "code": "        LeaveApplication leaveApplication = getLeaveApplicationById(leaveId);"}, {"line": 1053, "code": "        "}, {"line": 1054, "code": "        if (leaveApplication == null) {"}, {"line": 1055, "code": "            throw new ValidationException(\"Invalid leave application reference\");"}, {"line": 1056, "code": "        }"}, {"line": 1057, "code": "        "}, {"line": 1058, "code": "        if (leaveApplication.getNumDays() <= 3) {"}, {"line": 1059, "code": "            throw new ValidationException(\"This leave application should be processed by a Manager, not HR\");"}, {"line": 1060, "code": "        }"}, {"line": 1061, "code": "        "}, {"line": 1062, "code": "        leaveApplication.setStatus(status);"}, {"line": 1063, "code": "        leaveApplication.setRemarks(remarks);"}, {"line": 1064, "code": "        leaveApplication.setApprovedBy(approvedBy);"}, {"line": 1065, "code": "        "}, {"line": 1066, "code": "        leaveApplication.validate();"}, {"line": 1067, "code": "        "}, {"line": 1068, "code": "        return leaveApplicationDAO.save(leaveApplication);"}, {"line": 1069, "code": "    }"}, {"line": 1070, "code": "    "}, {"line": 1071, "code": "    @Override"}, {"line": 1072, "code": "    public LeaveApplication getLeaveApplicationById(String leaveId) throws SQLException {"}, {"line": 1073, "code": "        Optional<LeaveApplication> application = leaveApplicationDAO.findById(leaveId);"}, {"line": 1074, "code": "        return application.orElse(null);"}, {"line": 1075, "code": "    }"}, {"line": 1076, "code": "    "}, {"line": 1077, "code": "    @Override"}, {"line": 1078, "code": "    public List<LeaveSubType> getLeaveSubTypesByLeaveType(String leaveType) throws SQLException {"}, {"line": 1079, "code": "        return leaveSubTypeDAO.findByLeaveTypeAndActiveTrue(leaveType);"}, {"line": 1080, "code": "    }"}, {"line": 1081, "code": "    "}, {"line": 1082, "code": "    @Override"}, {"line": 1083, "code": "    public String generateLeaveId() {"}, {"line": 1084, "code": "        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(\"yyyyMMdd\");"}, {"line": 1085, "code": "        String datePart = LocalDate.now().format(formatter);"}, {"line": 1086, "code": "        String randomPart = String.format(\"%04d\", (int) (Math.random() * 10000));"}, {"line": 1087, "code": "        return \"LV-\" + datePart + \"-\" + randomPart;"}, {"line": 1088, "code": "    }"}, {"line": 1089, "code": "}"}, {"line": 1090, "code": ""}, {"line": 1091, "code": "package com.leavemanagement.util;"}, {"line": 1092, "code": ""}, {"line": 1093, "code": "import java.sql.Connection;"}, {"line": 1094, "code": "import java.sql.DriverManager;"}, {"line": 1095, "code": "import java.sql.SQLException;"}, {"line": 1096, "code": ""}, {"line": 1097, "code": "public class DatabaseConnection {"}, {"line": 1098, "code": "    "}, {"line": 1099, "code": "    private static final String URL = \"************************************************\";"}, {"line": 1100, "code": "    private static final String USER = \"leaveuser\";"}, {"line": 1101, "code": "    private static final String PASSWORD = \"leavepass\";"}, {"line": 1102, "code": "    "}, {"line": 1103, "code": "    public static Connection getConnection() throws SQLException {"}, {"line": 1104, "code": "        try {"}, {"line": 1105, "code": "            Class.forName(\"org.postgresql.Driver\");"}, {"line": 1106, "code": "            return DriverManager.getConnection(URL, USER, PASSWORD);"}, {"line": 1107, "code": "        } catch (ClassNotFoundException e) {"}, {"line": 1108, "code": "            throw new SQLException(\"Database driver not found\", e);"}, {"line": 1109, "code": "        }"}, {"line": 1110, "code": "    }"}, {"line": 1111, "code": "    "}, {"line": 1112, "code": "    public static void initializeDatabase() {"}, {"line": 1113, "code": "        try (Connection conn = getConnection()) {"}, {"line": 1114, "code": "            conn.createStatement().execute("}, {"line": 1115, "code": "                \"CREATE TABLE IF NOT EXISTS leave_application (\" +"}, {"line": 1116, "code": "                \"leave_id VARCHAR(50) PRIMARY KEY, \" +"}, {"line": 1117, "code": "                \"employee_id VARCHAR(50) NOT NULL, \" +"}, {"line": 1118, "code": "                \"start_date DATE NOT NULL, \" +"}, {"line": 1119, "code": "                \"end_date DATE NOT NULL, \" +"}, {"line": 1120, "code": "                \"num_days INT NOT NULL, \" +"}, {"line": 1121, "code": "                \"reason TEXT NOT NULL, \" +"}, {"line": 1122, "code": "                \"status VARCHAR(20) NOT NULL, \" +"}, {"line": 1123, "code": "                \"remarks TEXT, \" +"}, {"line": 1124, "code": "                \"approved_by VARCHAR(50), \" +"}, {"line": 1125, "code": "                \"leave_type VARCHAR(20) NOT NULL, \" +"}, {"line": 1126, "code": "                \"leave_sub_type VARCHAR(50)\" +"}, {"line": 1127, "code": "                \")\""}, {"line": 1128, "code": "            );"}, {"line": 1129, "code": "            "}, {"line": 1130, "code": "            conn.createStatement().execute("}, {"line": 1131, "code": "                \"CREATE TABLE IF NOT EXISTS leave_sub_type (\" +"}, {"line": 1132, "code": "                \"sub_type_id VARCHAR(50) PRIMARY KEY, \" +"}, {"line": 1133, "code": "                \"leave_type VARCHAR(50) NOT NULL, \" +"}, {"line": 1134, "code": "                \"sub_type_name VARCHAR(100) NOT NULL, \" +"}, {"line": 1135, "code": "                \"active BOOLEAN DEFAULT true\" +"}, {"line": 1136, "code": "                \")\""}, {"line": 1137, "code": "            );"}, {"line": 1138, "code": "            "}, {"line": 1139, "code": "            conn.createStatement().execute("}, {"line": 1140, "code": "                \"CREATE TABLE IF NOT EXISTS \\\"user\\\" (\" +"}, {"line": 1141, "code": "                \"user_id VARCHAR(50) PRIMARY KEY, \" +"}, {"line": 1142, "code": "                \"username VARCHAR(50) NOT NULL UNIQUE, \" +"}, {"line": 1143, "code": "                \"email VARCHAR(100) NOT NULL UNIQUE, \" +"}, {"line": 1144, "code": "                \"first_name VARCHAR(100), \" +"}, {"line": 1145, "code": "                \"last_name VARCHAR(100), \" +"}, {"line": 1146, "code": "                \"status VARCHAR(20) NOT NULL, \" +"}, {"line": 1147, "code": "                \"password_hash VARCHAR(255), \" +"}, {"line": 1148, "code": "                \"disabled BOOLEAN DEFAULT false, \" +"}, {"line": 1149, "code": "                \"organization VARCHAR(100), \" +"}, {"line": 1150, "code": "                \"team VARCHAR(100)\" +"}, {"line": 1151, "code": "                \")\""}, {"line": 1152, "code": "            );"}, {"line": 1153, "code": "            "}, {"line": 1154, "code": "            conn.createStatement().execute("}, {"line": 1155, "code": "                \"CREATE TABLE IF NOT EXISTS role (\" +"}, {"line": 1156, "code": "                \"role_id VARCHAR(50) PRIMARY KEY, \" +"}, {"line": 1157, "code": "                \"name VARCHAR(50) NOT NULL, \" +"}, {"line": 1158, "code": "                \"description TEXT, \" +"}, {"line": 1159, "code": "                \"inherits_from VARCHAR(50) REFERENCES role(role_id), \" +"}, {"line": 1160, "code": "                \"tenant_id VARCHAR(50)\" +"}, {"line": 1161, "code": "                \")\""}, {"line": 1162, "code": "            );"}, {"line": 1163, "code": "            "}, {"line": 1164, "code": "            conn.createStatement().execute("}, {"line": 1165, "code": "                \"CREATE TABLE IF NOT EXISTS user_role (\" +"}, {"line": 1166, "code": "                \"user_id VARCHAR(50) REFERENCES \\\"user\\\"(user_id), \" +"}, {"line": 1167, "code": "                \"role_id VARCHAR(50) REFERENCES role(role_id), \" +"}, {"line": 1168, "code": "                \"PRIMARY KEY (user_id, role_id)\" +"}, {"line": 1169, "code": "                \")\""}, {"line": 1170, "code": "            );"}, {"line": 1171, "code": "            "}, {"line": 1172, "code": "        } catch (SQLException e) {"}, {"line": 1173, "code": "            System.err.println(\"Error initializing database: \" + e.getMessage());"}, {"line": 1174, "code": "            e.printStackTrace();"}, {"line": 1175, "code": "        }"}, {"line": 1176, "code": "    }"}, {"line": 1177, "code": "}"}, {"line": 1178, "code": ""}, {"line": 1179, "code": "package com.leavemanagement;"}, {"line": 1180, "code": ""}, {"line": 1181, "code": "import com.leavemanagement.model.*;"}, {"line": 1182, "code": "import com.leavemanagement.service.*;"}, {"line": 1183, "code": "import com.leavemanagement.util.DatabaseConnection;"}, {"line": 1184, "code": ""}, {"line": 1185, "code": "import java.time.LocalDate;"}, {"line": 1186, "code": "import java.sql.SQLException;"}, {"line": 1187, "code": ""}, {"line": 1188, "code": "public class LeaveManagementApplication {"}, {"line": 1189, "code": "    "}, {"line": 1190, "code": "    public static void main(String[] args) {"}, {"line": 1191, "code": "        DatabaseConnection.initializeDatabase();"}, {"line": 1192, "code": "        "}, {"line": 1193, "code": "        LeaveApplicationService leaveService = new LeaveApplicationServiceImpl();"}, {"line": 1194, "code": "        "}, {"line": 1195, "code": "        try {"}, {"line": 1196, "code": "            System.out.println(\"Creating a sample leave application...\");"}, {"line": 1197, "code": "            "}, {"line": 1198, "code": "            LeaveApplication leaveApplication = new LeaveApplication();"}, {"line": 1199, "code": "            leaveApplication.setEmployeeId(\"EMP001\");"}, {"line": 1200, "code": "            leaveApplication.setStartDate(LocalDate.now().plusDays(5));"}, {"line": 1201, "code": "            leaveApplication.setEndDate(LocalDate.now().plusDays(10));"}, {"line": 1202, "code": "            leaveApplication.setReason(\"Taking time off for a family vacation\");"}, {"line": 1203, "code": "            leaveApplication.setLeaveType(LeaveType.ANNUAL_LEAVE);"}, {"line": 1204, "code": "            leaveApplication.setLeaveSubType(\"AL001\");"}, {"line": 1205, "code": "            "}, {"line": 1206, "code": "            LeaveApplication savedApplication = leaveService.applyForLeave(leaveApplication);"}, {"line": 1207, "code": "            System.out.println(\"Leave application created with ID: \" + savedApplication.getLeaveId());"}, {"line": 1208, "code": "            System.out.println(\"Number of days calculated: \" + savedApplication.getNumDays());"}, {"line": 1209, "code": "            "}, {"line": 1210, "code": "            LeaveApplication shortLeave = new LeaveApplication();"}, {"line": 1211, "code": "            shortLeave.setEmployeeId(\"EMP002\");"}, {"line": 1212, "code": "            shortLeave.setStartDate(LocalDate.now().plusDays(1));"}, {"line": 1213, "code": "            shortLeave.setEndDate(LocalDate.now().plusDays(2));"}, {"line": 1214, "code": "            shortLeave.setReason(\"Short medical appointment for routine checkup\");"}, {"line": 1215, "code": "            shortLeave.setLeaveType(LeaveType.SICK_LEAVE);"}, {"line": 1216, "code": "            shortLeave.setLeaveSubType(\"SL001\");"}, {"line": 1217, "code": "            "}, {"line": 1218, "code": "            LeaveApplication savedShortLeave = leaveService.applyForLeave(shortLeave);"}, {"line": 1219, "code": "            System.out.println(\"Short leave application created with ID: \" + savedShortLeave.getLeaveId());"}, {"line": 1220, "code": "            "}, {"line": 1221, "code": "            LeaveApplication approvedShortLeave = leaveService.managerApproval("}, {"line": 1222, "code": "                savedShortLeave.getLeaveId(),"}, {"line": 1223, "code": "                LeaveStatus.APPROVED,"}, {"line": 1224, "code": "                \"Approved as requested\","}, {"line": 1225, "code": "                \"MGR001\""}, {"line": 1226, "code": "            );"}, {"line": 1227, "code": "            System.out.println(\"Short leave approved by Manager: \" + approvedShortLeave.getStatus());"}, {"line": 1228, "code": "            "}, {"line": 1229, "code": "            LeaveApplication approvedLongLeave = leaveService.hrManagerApproval("}, {"line": 1230, "code": "                savedApplication.getLeaveId(),"}, {"line": 1231, "code": "                LeaveStatus.APPROVED,"}, {"line": 1232, "code": "                \"Approved after checking leave balance\","}, {"line": 1233, "code": "                \"HR001\""}, {"line": 1234, "code": "            );"}, {"line": 1235, "code": "            System.out.println(\"Long leave approved by HR Manager: \" + approvedLongLeave.getStatus());"}, {"line": 1236, "code": "            "}, {"line": 1237, "code": "            LeaveApplication rejectedLeave = leaveService.hrManagerApproval("}, {"line": 1238, "code": "                savedApplication.getLeaveId(),"}, {"line": 1239, "code": "                LeaveStatus.REJECTED,"}, {"line": 1240, "code": "                \"Insufficient leave balance\","}, {"line": 1241, "code": "                \"HR001\""}, {"line": 1242, "code": "            );"}, {"line": 1243, "code": "            System.out.println(\"Leave rejected by HR Manager: \" + rejectedLeave.getStatus());"}, {"line": 1244, "code": "            System.out.println(\"Rejection reason: \" + rejectedLeave.getRemarks());"}, {"line": 1245, "code": "            "}, {"line": 1246, "code": "        } catch (SQLException e) {"}, {"line": 1247, "code": "            System.err.println(\"Database error: \" + e.getMessage());"}, {"line": 1248, "code": "            e.printStackTrace();"}, {"line": 1249, "code": "        } catch (ValidationException e) {"}, {"line": 1250, "code": "            System.err.println(\"Validation error: \" + e.getMessage());"}, {"line": 1251, "code": "        }"}, {"line": 1252, "code": "    }"}, {"line": 1253, "code": "}"}], "stacks": [{"stack_name": "LeaveApplication Entity", "steps": [{"id": "E1", "sentence_id": 1, "sentence": "LeaveApplication has leave id^PK, employee id^FK, start date, end date, number of days[derived], reason, status (Pending, Approved, Rejected), remarks, approved by, leave type (Annual Leave, Sick Leave, Parental Leave, Bereavement), leave subtype.", "color": "#5C6BC0", "inner_components": [{"id": "A0", "sentence_id": 10000, "sentence": "LeaveApplication has", "related_lines": [18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256], "related_keywords": [{"keyword": "public class", "description": "Class declaration for LeaveApplication entity"}, {"keyword": "private", "description": "Access modifier for encapsulated fields"}, {"keyword": "String", "description": "Data type for text fields like leaveId and employeeId"}, {"keyword": "LocalDate", "description": "Date type for startDate and endDate"}, {"keyword": "Integer", "description": "Data type for numDays field"}, {"keyword": "LeaveStatus", "description": "Enum type for application status (Pending/Approved/Rejected)"}, {"keyword": "LeaveType", "description": "Enum type for leave categories (Annual/Sick/Parental/Bereavement)"}, {"keyword": "constructor", "description": "Default and parameterized constructors"}, {"keyword": "builder", "description": "Builder pattern implementation for creating instances"}, {"keyword": "getters", "description": "Methods to access private fields"}, {"keyword": "setters", "description": "Methods to modify private fields"}, {"keyword": "calculateNumDays", "description": "Method to compute number of leave days from dates"}, {"keyword": "validate", "description": "Method to verify field values meet business rules"}, {"keyword": "ValidationException", "description": "Exception thrown when validation fails"}, {"keyword": "ChronoUnit", "description": "Time calculation utility for date difference"}, {"keyword": "entity", "description": "Java class representing database table"}, {"keyword": "model", "description": "Package location for domain entities"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A1", "sentence_id": 10001, "sentence": " leaveID^PK,", "related_lines": [20, 35, 38, 56, 68, 69, 70, 169, 170, 171, 173, 174, 175, 136, 137, 138, 763, 780, 786, 791, 1116, 1083, 1084, 1085, 1086, 1087], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "leaveId", "description": "Field name for leave identifier"}, {"keyword": "PRIMARY KEY", "description": "Database constraint ensuring uniqueness"}, {"keyword": "VARCHAR", "description": "Variable character database column type"}, {"keyword": "getLeaveId", "description": "Getter method for leave ID"}, {"keyword": "setLeaveId", "description": "Setter method for leave ID"}, {"keyword": "generateLeaveId", "description": "Method that creates new leave IDs"}, {"keyword": "LV-", "description": "Prefix used for leave ID generation"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A2", "sentence_id": 10002, "sentence": " employeeID^FK,", "related_lines": [21, 35, 39, 57, 73, 74, 75, 177, 178, 179, 181, 182, 183, 140, 141, 142, 764, 810, 816, 824, 1117], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "employeeId", "description": "Field name for employee identifier"}, {"keyword": "FK", "description": "Foreign Key referencing User table"}, {"keyword": "getEmployeeId", "description": "Getter method for employee ID"}, {"keyword": "setEmployeeId", "description": "Setter method for employee ID"}, {"keyword": "NOT NULL", "description": "Employee ID is required"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A3", "sentence_id": 10003, "sentence": " startDate,", "related_lines": [22, 35, 40, 58, 78, 79, 80, 185, 186, 187, 189, 190, 191, 144, 145, 146, 130, 131, 765, 793, 1118], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "LocalDate", "description": "Java type for date without time"}, {"keyword": "startDate", "description": "Field name for leave start date"}, {"keyword": "getStartDate", "description": "Getter method for start date"}, {"keyword": "setStartDate", "description": "Setter method for start date"}, {"keyword": "DATE", "description": "Database column type for dates"}, {"keyword": "NOT NULL", "description": "Start date is required"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A4", "sentence_id": 10004, "sentence": " endDate,", "related_lines": [23, 35, 41, 59, 83, 84, 85, 193, 194, 195, 197, 198, 199, 148, 149, 150, 152, 153, 154, 130, 131, 766, 794, 1119], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "LocalDate", "description": "Java type for date without time"}, {"keyword": "endDate", "description": "Field name for leave end date"}, {"keyword": "getEndDate", "description": "Getter method for end date"}, {"keyword": "setEndDate", "description": "Setter method for end date"}, {"keyword": "DATE", "description": "Database column type for dates"}, {"keyword": "NOT NULL", "description": "End date is required"}, {"keyword": "isBefore", "description": "Method to check if end date is before start date"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A5", "sentence_id": 10005, "sentence": " numDays,", "related_lines": [24, 36, 42, 60, 88, 89, 90, 201, 202, 203, 205, 206, 207, 129, 130, 131, 132, 133, 767, 795, 1120, 1036, 1037, 1038, 1058, 1059, 1060], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "Integer", "description": "Wrapper class for whole numbers"}, {"keyword": "numDays", "description": "Field name for number of leave days"}, {"keyword": "derived", "description": "Calculated field based on start and end dates"}, {"keyword": "getNumDays", "description": "Getter method for number of days"}, {"keyword": "setNumDays", "description": "Setter method for number of days"}, {"keyword": "calculateNumDays", "description": "Method that calculates days between dates"}, {"keyword": "ChronoUnit.DAYS.between", "description": "Java utility to calculate days between dates"}, {"keyword": "INT", "description": "Database column type for integers"}, {"keyword": "NOT NULL", "description": "Number of days is required"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A6", "sentence_id": 10006, "sentence": " reason,", "related_lines": [25, 36, 43, 61, 93, 94, 95, 209, 210, 211, 213, 214, 215, 156, 157, 158, 768, 796, 1121], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "reason", "description": "Field name for leave reason"}, {"keyword": "getReason", "description": "Getter method for reason"}, {"keyword": "setReason", "description": "Setter method for reason"}, {"keyword": "TEXT", "description": "Database column type for long text"}, {"keyword": "NOT NULL", "description": "Reason is required"}, {"keyword": "trim", "description": "Method to remove leading/trailing spaces"}, {"keyword": "length", "description": "Method to check string length"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A7", "sentence_id": 10007, "sentence": " status (Pending, Approved, Rejected),", "related_lines": [26, 36, 44, 62, 98, 99, 100, 217, 218, 219, 221, 222, 223, 258, 259, 260, 261, 263, 264, 265, 266, 267, 268, 269, 270, 271, 1020, 1040, 1062, 769, 797, 1122], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "LeaveStatus", "description": "Enum type for leave status"}, {"keyword": "status", "description": "Field name for current leave status"}, {"keyword": "getStatus", "description": "Getter method for status"}, {"keyword": "setStatus", "description": "Setter method for status"}, {"keyword": "enum", "description": "Java construct for fixed set of constants"}, {"keyword": "PENDING", "description": "Status value for pending approval"}, {"keyword": "APPROVED", "description": "Status value for approved leave"}, {"keyword": "REJECTED", "description": "Status value for rejected leave"}, {"keyword": "VARCHAR", "description": "Database column type for text"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A8", "sentence_id": 10008, "sentence": " remarks,", "related_lines": [27, 36, 45, 63, 103, 104, 105, 225, 226, 227, 229, 230, 231, 164, 165, 166, 770, 798, 1123], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "remarks", "description": "Field name for approval/rejection comments"}, {"keyword": "getRemarks", "description": "Getter method for remarks"}, {"keyword": "setRemarks", "description": "Setter method for remarks"}, {"keyword": "TEXT", "description": "Database column type for text"}, {"keyword": "conditional", "description": "Required when status is REJECTED"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A9", "sentence_id": 10009, "sentence": " approvedBy,", "related_lines": [28, 37, 46, 64, 108, 109, 110, 233, 234, 235, 237, 238, 239, 1042, 1064, 771, 799, 1124], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "approvedBy", "description": "Field name for approver identifier"}, {"keyword": "getApprovedBy", "description": "Getter method for approver"}, {"keyword": "setApprovedBy", "description": "Setter method for approver"}, {"keyword": "VARCHAR", "description": "Database column type for text"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A10", "sentence_id": 100010, "sentence": " leaveType (Annual Leave, Sick Leave, Parental Leave, Bereavement),", "related_lines": [29, 37, 47, 65, 113, 114, 115, 241, 242, 243, 245, 246, 247, 283, 284, 285, 286, 287, 289, 290, 291, 292, 293, 294, 295, 296, 297, 160, 161, 162, 772, 800, 1125], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "LeaveType", "description": "Enum type for leave categories"}, {"keyword": "leaveType", "description": "Field name for type of leave"}, {"keyword": "getLeaveType", "description": "Getter method for leave type"}, {"keyword": "setLeaveType", "description": "Setter method for leave type"}, {"keyword": "enum", "description": "Java construct for fixed set of constants"}, {"keyword": "ANNUAL_LEAVE", "description": "Leave type for vacation"}, {"keyword": "SICK_LEAVE", "description": "Leave type for illness"}, {"keyword": "PARENTAL_LEAVE", "description": "Leave type for new parents"}, {"keyword": "BEREAVEMENT", "description": "Leave type for family loss"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "NOT NULL", "description": "Leave type is required"}], "clickable": true, "color": "#5C6BC0"}, {"id": "A11", "sentence_id": 100011, "sentence": " leaveSubType.", "related_lines": [30, 37, 48, 66, 118, 119, 120, 249, 250, 251, 253, 254, 255, 773, 801, 1126], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "leaveSubType", "description": "Field name for specific leave sub-category"}, {"keyword": "getLeaveSubType", "description": "Getter method for leave sub-type"}, {"keyword": "setLeaveSubType", "description": "Setter method for leave sub-type"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "dependent", "description": "Depends on selected leaveType"}], "clickable": true, "color": "#5C6BC0"}], "related_lines": [], "related_keywords": []}, {"id": "E1.1", "sentence_id": 2, "sentence": "LeaveApplication has many-to-one relationship with User using LeaveApplication.employeeID to User.user_id^FK", "color": "#10B981", "related_lines": [21, 177, 181, 396, 510, 514, 1117, 1141, 1166], "related_keywords": [{"keyword": "private", "description": "Only this class can access this field"}, {"keyword": "String", "description": "Text field for employee ID"}, {"keyword": "employeeId", "description": "Field name for employee identifier"}, {"keyword": "user_id", "description": "Primary key in User table"}, {"keyword": "REFERENCES", "description": "Foreign key constraint"}, {"keyword": "VARCHAR", "description": "Database column type for text"}]}, {"id": "E1.2", "sentence_id": 3, "sentence": "LeaveApplication has many-to-one relationship with LeaveSubType using LeaveApplication.leaveType and LeaveApplication.leaveSubType to LeaveSubType.leaveType and LeaveSubType.subTypeId", "color": "#7986CB", "related_lines": [30, 66, 249, 253, 312, 313, 341, 346, 879, 880, 773, 801, 1078, 1126, 1132, 1133], "related_keywords": [{"keyword": "leaveSubType", "description": "Field linking to leave sub-type"}, {"keyword": "leaveType", "description": "Field for main leave category"}, {"keyword": "subTypeId", "description": "Primary key in LeaveSubType table"}, {"keyword": "findByLeaveTypeAndActiveTrue", "description": "Method to fetch active sub-types for a leave type"}, {"keyword": "VARCHAR", "description": "Database column type"}]}, {"id": "E1.3", "sentence_id": 4, "sentence": "LeaveApplication.leaveID DEFAULT_PREFIX = \"LV\"", "color": "#26A69A", "related_lines": [1083, 1084, 1085, 1086, 1087], "related_keywords": [{"keyword": "generateLeaveId", "description": "Method that generates new leave IDs"}, {"keyword": "return", "description": "Returns the generated ID"}, {"keyword": "DateTimeF<PERSON>atter", "description": "Tool for formatting dates"}, {"keyword": "format", "description": "Format the date and number parts"}, {"keyword": "LV-", "description": "The prefix used for leave IDs"}]}, {"id": "E1.4", "sentence_id": 5, "sentence": "LeaveApplication.status DEFAULT_VALUE = \"Pending\"", "color": "#4DB6AC", "related_lines": [1020, 259, 26], "related_keywords": [{"keyword": "setStatus", "description": "Method to set the status"}, {"keyword": "LeaveStatus", "description": "Enum type for status values"}, {"keyword": "PENDING", "description": "Default status value"}, {"keyword": "enum", "description": "Type definition for fixed values"}]}, {"id": "E1.5", "sentence_id": 6, "sentence": "LeaveApplication.leaveID must be unique", "color": "#FFB300", "related_lines": [20, 136, 137, 138, 169, 170, 171, 173, 174, 175, 1116], "related_keywords": [{"keyword": "private", "description": "Only this class can access this field, nobody else"}, {"keyword": "String", "description": "This field stores text (like words or letters)"}, {"keyword": "if", "description": "Check if something is true"}, {"keyword": "==", "description": "Is this exactly equal to that?"}, {"keyword": "null", "description": "Nothing - no value"}, {"keyword": "||", "description": "OR - if either this OR that is true"}, {"keyword": "trim", "description": "Remove extra spaces at the beginning and end"}, {"keyword": "isEmpty", "description": "Check if there's nothing written here"}, {"keyword": "throw", "description": "Create an error message"}, {"keyword": "new", "description": "Create a new thing (like a new error message)"}, {"keyword": "public", "description": "Anyone can use this method"}, {"keyword": "String", "description": "This method gives back text"}, {"keyword": "return", "description": "Give back this value when someone calls this method"}, {"keyword": "public", "description": "Anyone can use this method"}, {"keyword": "void", "description": "This method doesn't give anything back"}, {"keyword": "this", "description": "Referring to this specific object we're working with"}, {"keyword": "VARCHAR", "description": "A database column for storing text up to a certain length"}, {"keyword": "PRIMARY", "description": "This is the main ID"}, {"keyword": "KEY", "description": "This makes sure each row has a unique ID"}]}, {"id": "E1.6", "sentence_id": 7, "sentence": "LeaveApplication.startDate must be a valid date", "color": "#FFD54F", "related_lines": [22, 144, 145, 146, 185, 186, 187, 189, 190, 191, 1118], "related_keywords": [{"keyword": "private", "description": "Only this class can see this"}, {"keyword": "LocalDate", "description": "A date (like December 25, 2024)"}, {"keyword": "if", "description": "Check if"}, {"keyword": "==", "description": "Is equal to"}, {"keyword": "null", "description": "No date selected"}, {"keyword": "throw", "description": "Show an error"}, {"keyword": "new", "description": "Create a new error message"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "LocalDate", "description": "Returns a date"}, {"keyword": "return", "description": "Give back the start date"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "void", "description": "Doesn't return anything"}, {"keyword": "LocalDate", "description": "Takes a date as input"}, {"keyword": "this", "description": "This specific object"}, {"keyword": "DATE", "description": "Database column for storing dates"}, {"keyword": "NOT", "description": "Cannot be"}, {"keyword": "NULL", "description": "Must have a date"}]}, {"id": "E1.7", "sentence_id": 8, "sentence": "LeaveApplication.endDate must be after startDate", "color": "#FFB300", "related_lines": [23, 152, 153, 154, 193, 194, 195, 197, 198, 199, 1119], "related_keywords": [{"keyword": "private", "description": "Only this class can see this"}, {"keyword": "LocalDate", "description": "A date value"}, {"keyword": "if", "description": "Check if"}, {"keyword": "isBefore", "description": "Is this date before that date?"}, {"keyword": "throw", "description": "Show an error"}, {"keyword": "new", "description": "Create an error message"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "LocalDate", "description": "Returns a date"}, {"keyword": "return", "description": "Give back the end date"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "void", "description": "Doesn't return anything"}, {"keyword": "LocalDate", "description": "Takes a date as input"}, {"keyword": "this", "description": "This specific object"}, {"keyword": "DATE", "description": "Database column for dates"}, {"keyword": "NOT", "description": "Cannot be"}, {"keyword": "NULL", "description": "Must have a date"}]}, {"id": "E1.8", "sentence_id": 9, "sentence": "LeaveApplication.reason must be at least 10 characters", "color": "#8E24AA", "related_lines": [25, 156, 157, 158, 209, 210, 211, 213, 214, 215, 1121], "related_keywords": [{"keyword": "private", "description": "Only this class can see this"}, {"keyword": "String", "description": "Text field"}, {"keyword": "if", "description": "Check if"}, {"keyword": "==", "description": "Is equal to"}, {"keyword": "null", "description": "No reason given"}, {"keyword": "||", "description": "OR"}, {"keyword": "trim", "description": "Remove extra spaces"}, {"keyword": "length", "description": "How many characters long?"}, {"keyword": "<", "description": "Is less than"}, {"keyword": "10", "description": "The number ten"}, {"keyword": "throw", "description": "Show an error"}, {"keyword": "new", "description": "Create an error message"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "String", "description": "Returns text"}, {"keyword": "return", "description": "Give back the reason"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "void", "description": "Doesn't return anything"}, {"keyword": "String", "description": "Takes text as input"}, {"keyword": "this", "description": "This specific object"}, {"keyword": "TEXT", "description": "Database column for long text"}, {"keyword": "NOT", "description": "Cannot be"}, {"keyword": "NULL", "description": "Must have text"}]}, {"id": "E1.9", "sentence_id": 10, "sentence": "LeaveApplication.status must be one of \"Pending\", \"Approved\", \"Rejected\"", "color": "#BA68C8", "related_lines": [26, 217, 218, 219, 221, 222, 223, 258, 259, 260, 261, 1020, 1122], "related_keywords": [{"keyword": "private", "description": "Only this class can see this"}, {"keyword": "LeaveStatus", "description": "A custom type that can only be Pending, Approved, or Rejected"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "LeaveStatus", "description": "Returns the status type"}, {"keyword": "return", "description": "Give back the current status"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "void", "description": "Doesn't return anything"}, {"keyword": "LeaveStatus", "description": "Takes a status as input"}, {"keyword": "this", "description": "This specific object"}, {"keyword": "enum", "description": "A list of fixed choices"}, {"keyword": "PENDING", "description": "One choice: waiting for approval"}, {"keyword": "APPROVED", "description": "One choice: leave was approved"}, {"keyword": "REJECTED", "description": "One choice: leave was denied"}, {"keyword": "setStatus", "description": "Change the status"}, {"keyword": "LeaveStatus", "description": "The status type"}, {"keyword": "PENDING", "description": "Set to waiting for approval"}, {"keyword": "VARCHAR", "description": "Database column for text"}, {"keyword": "NOT", "description": "Cannot be"}, {"keyword": "NULL", "description": "Must have a status"}]}, {"id": "E1.10", "sentence_id": 11, "sentence": "LeaveApplication.remarks must be provided when status is \"Rejected\"", "color": "#66BB6A", "related_lines": [27, 164, 165, 166, 225, 226, 227, 229, 230, 231, 1123], "related_keywords": [{"keyword": "private", "description": "Only this class can see this"}, {"keyword": "String", "description": "Text field"}, {"keyword": "if", "description": "Check if"}, {"keyword": "==", "description": "Is equal to"}, {"keyword": "LeaveStatus", "description": "The status type"}, {"keyword": "REJECTED", "description": "Leave was denied"}, {"keyword": "&&", "description": "AND - both must be true"}, {"keyword": "null", "description": "No remarks given"}, {"keyword": "||", "description": "OR"}, {"keyword": "trim", "description": "Remove extra spaces"}, {"keyword": "isEmpty", "description": "Nothing written"}, {"keyword": "throw", "description": "Show an error"}, {"keyword": "new", "description": "Create an error message"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "String", "description": "Returns text"}, {"keyword": "return", "description": "Give back the remarks"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "void", "description": "Doesn't return anything"}, {"keyword": "String", "description": "Takes text as input"}, {"keyword": "this", "description": "This specific object"}, {"keyword": "TEXT", "description": "Database column for text comments"}]}, {"id": "E1.11", "sentence_id": 12, "sentence": "LeaveApplication.numDays is calculated based on startDate and endDate", "color": "#F81C784", "related_lines": [24, 129, 130, 131, 132, 133, 201, 202, 203, 205, 206, 207, 1018, 1120], "related_keywords": [{"keyword": "private", "description": "Only this class can see this"}, {"keyword": "Integer", "description": "A whole number (like 5 or 10)"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "void", "description": "Doesn't return anything"}, {"keyword": "if", "description": "Check if"}, {"keyword": "!=", "description": "Is not equal to"}, {"keyword": "null", "description": "No value"}, {"keyword": "&&", "description": "AND - both must be true"}, {"keyword": "=", "description": "Set this equal to"}, {"keyword": "int", "description": "A whole number"}, {"keyword": "ChronoUnit", "description": "A tool for calculating time differences"}, {"keyword": "DAYS", "description": "Count in days"}, {"keyword": "between", "description": "Calculate difference between two dates"}, {"keyword": "+", "description": "Add"}, {"keyword": "1", "description": "The number one"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "Integer", "description": "Returns a whole number"}, {"keyword": "return", "description": "Give back the number of days"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "void", "description": "Doesn't return anything"}, {"keyword": "Integer", "description": "Takes a whole number as input"}, {"keyword": "this", "description": "This specific object"}, {"keyword": "calculateNumDays", "description": "Call the method that calculates days"}, {"keyword": "INT", "description": "Database column for whole numbers"}, {"keyword": "NOT", "description": "Cannot be"}, {"keyword": "NULL", "description": "Must have a number"}]}]}, {"stack_name": "LeaveSubType Entity", "steps": [{"id": "E2", "sentence_id": 13, "sentence": "LeaveSubType has leaveType, subTypeId^PK, subTypeName, active.", "color": "#06B6D4", "related_lines": [], "inner_components": [{"id": "A0", "sentence_id": 130000, "sentence": "LeaveSubType has", "related_lines": [], "related_keywords": [], "clickable": false, "color": "#06B6D4"}, {"id": "A1", "sentence_id": 10001, "sentence": " leaveType,", "related_lines": [311, 319, 320, 331, 336, 337, 338, 361, 362, 363, 365, 366, 367, 892, 1132], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "leaveType", "description": "Field name for main leave category"}, {"keyword": "getLeaveType", "description": "Getter method for leave type"}, {"keyword": "setLeaveType", "description": "Setter method for leave type"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "NOT NULL", "description": "Leave type is required"}], "clickable": true, "color": "#06B6D4"}, {"id": "A2", "sentence_id": 130002, "sentence": " subTypeId^PK,", "related_lines": [312, 319, 321, 332, 341, 342, 343, 369, 370, 371, 373, 374, 375, 879, 880, 885, 893, 1133], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "subTypeId", "description": "Field name for sub-type identifier"}, {"keyword": "PK", "description": "Primary Key - unique identifier"}, {"keyword": "getSubTypeId", "description": "Getter method for sub-type ID"}, {"keyword": "setSubTypeId", "description": "Setter method for sub-type ID"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "PRIMARY KEY", "description": "Database constraint ensuring uniqueness"}], "clickable": true, "color": "#06B6D4"}, {"id": "A3", "sentence_id": 130003, "sentence": " subTypeName", "related_lines": [313, 319, 322, 333, 346, 347, 348, 377, 378, 379, 381, 382, 383, 894, 1134], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "subTypeName", "description": "Field name for sub-type name/description"}, {"keyword": "getSubTypeName", "description": "Getter method for sub-type name"}, {"keyword": "setSubTypeName", "description": "Setter method for sub-type name"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "NOT NULL", "description": "Sub-type name is required"}], "clickable": true, "color": "#06B6D4"}, {"id": "A4", "sentence_id": 130004, "sentence": " active.", "related_lines": [314, 319, 323, 334, 351, 352, 353, 385, 386, 387, 389, 390, 391, 880, 895, 1135], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "Boolean", "description": "Data type for true/false values"}, {"keyword": "active", "description": "Field name for activation status"}, {"keyword": "getActive", "description": "Getter method for active status"}, {"keyword": "setActive", "description": "Setter method for active status"}, {"keyword": "BOOLEAN", "description": "Database column type for true/false"}, {"keyword": "DEFAULT true", "description": "Default value is true"}], "clickable": true, "color": "#06B6D4"}], "related_keywords": []}, {"id": "E2.1", "sentence_id": 14, "sentence": "LeaveSubType has one-to-many relationship with LeaveApplication using LeaveSubType.leaveType and LeaveSubType.subTypeId to LeaveApplication.leaveType and LeaveApplication.leaveSubType", "color": "#F97316", "related_lines": [30, 66, 249, 253, 311, 312, 313, 773, 801, 879, 880, 893, 894, 1126, 1132, 1133], "related_keywords": [{"keyword": "private", "description": "Fields are only accessible within their respective classes"}, {"keyword": "String", "description": "Data type for text fields"}, {"keyword": "leaveType", "description": "Shared field between both entities"}, {"keyword": "subTypeId", "description": "Primary key in LeaveSubType"}, {"keyword": "leaveSubType", "description": "Foreign key reference in LeaveApplication"}, {"keyword": "one-to-many", "description": "One LeaveSubType can have many LeaveApplications"}, {"keyword": "findByLeaveTypeAndActiveTrue", "description": "Method to find sub-types for a leave type"}, {"keyword": "VARCHAR", "description": "Database column type"}]}, {"id": "E2.2", "sentence_id": 15, "sentence": "LeaveSubType.active DEFAULT_VALUE = true", "color": "#F97316", "related_lines": [314, 323, 1135], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "Boolean", "description": "Data type for true/false values"}, {"keyword": "active", "description": "Field name for activation status"}, {"keyword": "=", "description": "Assignment operator"}, {"keyword": "true", "description": "Default boolean value"}, {"keyword": "BOOLEAN", "description": "Database column type"}, {"keyword": "DEFAULT", "description": "Database default value"}]}, {"id": "E2.3", "sentence_id": 16, "sentence": "LeaveSubType.leaveType must be one of \"Annual Leave\", \"Sick Leave\", \"Parental Leave\", \"Bereavement\"", "color": "#F97316", "related_lines": [29, 160, 161, 162, 241, 242, 243, 245, 246, 247, 283, 284, 285, 286, 287, 1125], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within the class"}, {"keyword": "LeaveType", "description": "Enum type for leave categories"}, {"keyword": "leaveType", "description": "Field that must match enum values"}, {"keyword": "enum", "description": "Java construct for fixed set of constants"}, {"keyword": "ANNUAL_LEAVE", "description": "Enum value for vacation days"}, {"keyword": "SICK_LEAVE", "description": "Enum value for sick days"}, {"keyword": "PARENTAL_LEAVE", "description": "Enum value for parental leave"}, {"keyword": "BEREAVEMENT", "description": "Enum value for bereavement leave"}, {"keyword": "VARCHAR", "description": "Database column type"}, {"keyword": "validation", "description": "Must validate against enum values"}]}, {"id": "E2.4", "sentence_id": 17, "sentence": "LeaveSubType.subTypeId must be unique within same leaveType", "color": "#F97316", "related_lines": [312, 321, 331, 332, 341, 342, 361, 369, 373, 893, 1133], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for sub-type ID"}, {"keyword": "subTypeId", "description": "Field that must be unique"}, {"keyword": "unique", "description": "Constraint for uniqueness"}, {"keyword": "leaveType", "description": "Field for grouping uniqueness"}, {"keyword": "PRIMARY KEY", "description": "Database constraint"}, {"keyword": "VARCHAR", "description": "Database column type"}, {"keyword": "composite", "description": "Uniqueness within a group"}]}]}, {"stack_name": "User Entity", "steps": [{"id": "E3", "sentence_id": 18, "sentence": "User has user_id^PK, username, email, first_name, last_name, status (active, inactive, suspended), password_hash, disabled, organization, team.", "color": "#6366F1", "related_lines": [], "inner_components": [{"id": "A0", "sentence_id": 180000, "sentence": "User has", "related_lines": [], "related_keywords": [], "clickable": false, "color": "#6366F1"}, {"id": "A1", "sentence_id": 180001, "sentence": " user_id^PK,", "related_lines": [396, 411, 414, 432, 444, 445, 446, 510, 511, 512, 514, 515, 516, 939, 947, 919, 1141], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "userId", "description": "Field name for user identifier"}, {"keyword": "PK", "description": "Primary Key - unique identifier"}, {"keyword": "getUserId", "description": "Getter method for user ID"}, {"keyword": "setUserId", "description": "Setter method for user ID"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "PRIMARY KEY", "description": "Database constraint ensuring uniqueness"}, {"keyword": "loadUserRoles", "description": "Method to load user's role assignments"}], "clickable": true, "color": "#6366F1"}, {"id": "A2", "sentence_id": 180002, "sentence": " username,", "related_lines": [397, 411, 415, 433, 449, 450, 451, 518, 519, 520, 522, 523, 524, 908, 914, 920, 1142], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "username", "description": "Field name for login identifier"}, {"keyword": "getUsername", "description": "Getter method for username"}, {"keyword": "setUsername", "description": "Setter method for username"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "NOT NULL UNIQUE", "description": "Username must be unique and not null"}, {"keyword": "findByUsername", "description": "Method to find user by username"}], "clickable": true, "color": "#6366F1"}, {"id": "A3", "sentence_id": 180003, "sentence": " email", "related_lines": [398, 411, 416, 434, 454, 455, 456, 526, 527, 528, 530, 531, 532, 921, 1143], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "email", "description": "Field name for email address"}, {"keyword": "getEmail", "description": "Getter method for email"}, {"keyword": "setEmail", "description": "Setter method for email"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "NOT NULL UNIQUE", "description": "Email must be unique and not null"}], "clickable": true, "color": "#6366F1"}, {"id": "A4", "sentence_id": 180004, "sentence": " first_name,", "related_lines": [399, 411, 417, 435, 459, 460, 461, 534, 535, 536, 538, 539, 540, 922, 1144], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "firstName", "description": "Field name for user's first name"}, {"keyword": "getFirstName", "description": "Getter method for first name"}, {"keyword": "setFirstName", "description": "Setter method for first name"}, {"keyword": "VARCHAR", "description": "Database column type for text"}], "clickable": true, "color": "#6366F1"}, {"id": "A5", "sentence_id": 180005, "sentence": " last_name,", "related_lines": [400, 411, 418, 436, 464, 465, 466, 542, 543, 544, 546, 547, 548, 923, 1145], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "lastName", "description": "Field name for user's last name"}, {"keyword": "getLastName", "description": "Getter method for last name"}, {"keyword": "setLastName", "description": "Setter method for last name"}, {"keyword": "VARCHAR", "description": "Database column type for text"}], "clickable": true, "color": "#6366F1"}, {"id": "A6", "sentence_id": 180006, "sentence": " status (active, inactive, suspended),", "related_lines": [401, 411, 419, 437, 469, 470, 471, 550, 551, 552, 554, 555, 556, 599, 600, 601, 602, 604, 605, 606, 607, 608, 609, 610, 611, 612, 924, 1146], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "UserStatus", "description": "Enum type for user status"}, {"keyword": "status", "description": "Field name for user's activation status"}, {"keyword": "getStatus", "description": "Getter method for status"}, {"keyword": "setStatus", "description": "Setter method for status"}, {"keyword": "enum", "description": "Java construct for fixed set of constants"}, {"keyword": "ACTIVE", "description": "Status value for active users"}, {"keyword": "INACTIVE", "description": "Status value for inactive users"}, {"keyword": "SUSPENDED", "description": "Status value for suspended users"}, {"keyword": "UserStatus.ACTIVE", "description": "Default status value"}, {"keyword": "VARCHAR", "description": "Database column type for text"}], "clickable": true, "color": "#6366F1"}, {"id": "A7", "sentence_id": 180007, "sentence": " password_hash,", "related_lines": [402, 412, 420, 438, 474, 475, 476, 558, 559, 560, 562, 563, 564, 925, 1147], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "passwordHash", "description": "Field name for encrypted password"}, {"keyword": "getPasswordHash", "description": "Getter method for password hash"}, {"keyword": "setPasswordHash", "description": "Setter method for password hash"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "security", "description": "Stores hashed password, not plain text"}], "clickable": true, "color": "#6366F1"}, {"id": "A8", "sentence_id": 180008, "sentence": " disabled,", "related_lines": [403, 412, 421, 439, 479, 480, 481, 566, 567, 568, 570, 571, 572, 926, 1148], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "Boolean", "description": "Data type for true/false values"}, {"keyword": "disabled", "description": "Field name for account disable status"}, {"keyword": "getDisabled", "description": "Getter method for disabled status"}, {"keyword": "setDisabled", "description": "Setter method for disabled status"}, {"keyword": "false", "description": "Default value - account enabled"}, {"keyword": "BOOLEAN", "description": "Database column type for true/false"}, {"keyword": "DEFAULT false", "description": "Default value is false (not disabled)"}], "clickable": true, "color": "#6366F1"}, {"id": "A9", "sentence_id": 180009, "sentence": " organization,", "related_lines": [404, 412, 422, 440, 484, 485, 486, 574, 575, 576, 578, 579, 580, 927, 1149], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "organization", "description": "Field name for user's organization"}, {"keyword": "getOrganization", "description": "Getter method for organization"}, {"keyword": "setOrganization", "description": "Setter method for organization"}, {"keyword": "VARCHAR", "description": "Database column type for text"}], "clickable": true, "color": "#6366F1"}, {"id": "A10", "sentence_id": 1800010, "sentence": " team.", "related_lines": [405, 413, 423, 441, 489, 490, 491, 582, 583, 584, 586, 587, 588, 928, 1150], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "team", "description": "Field name for user's team"}, {"keyword": "getTeam", "description": "Getter method for team"}, {"keyword": "setTeam", "description": "Setter method for team"}, {"keyword": "VARCHAR", "description": "Database column type for text"}], "clickable": true, "color": "#6366F1"}], "related_keywords": []}, {"id": "E3.1", "sentence_id": 19, "sentence": "User has one-to-many relationship with LeaveApplication using User.user_id^PK to LeaveApplication.employeeID^FK", "color": "#F97316", "related_lines": [21, 396, 414, 510, 177, 181, 764, 810, 816, 824, 1117, 1141, 1199], "related_keywords": [{"keyword": "private", "description": "Fields are only accessible within their respective classes"}, {"keyword": "String", "description": "Data type for text fields"}, {"keyword": "userId", "description": "Primary key in User table"}, {"keyword": "employeeId", "description": "Foreign key in LeaveApplication table"}, {"keyword": "PK", "description": "Primary Key constraint"}, {"keyword": "FK", "description": "Foreign Key constraint"}, {"keyword": "one-to-many", "description": "One User can have many LeaveApplications"}, {"keyword": "findByEmployeeId", "description": "Method to find applications by employee"}, {"keyword": "VARCHAR", "description": "Database column type"}]}, {"id": "E3.2", "sentence_id": 20, "sentence": "User has many-to-many relationship with Role using UserRole junction table", "color": "#F97316", "related_lines": [406, 424, 493, 494, 495, 496, 590, 594, 595, 940, 941, 942, 1165, 1166, 1167, 1168, 1169], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "List", "description": "Collection type for multiple roles"}, {"keyword": "Role", "description": "Entity type for user roles"}, {"keyword": "roles", "description": "Field name for user's assigned roles"}, {"keyword": "getRoles", "description": "Getter method for roles collection"}, {"keyword": "setRoles", "description": "Setter method for roles collection"}, {"keyword": "many-to-many", "description": "Users can have multiple roles, roles can belong to multiple users"}, {"keyword": "user_role", "description": "Junction table name"}, {"keyword": "PRIMARY KEY (user_id, role_id)", "description": "Composite primary key in junction table"}, {"keyword": "REFERENCES", "description": "Foreign key constraints"}]}, {"id": "E3.3", "sentence_id": 21, "sentence": "User.status DEFAULT_VALUE = \"active\"", "color": "#F97316", "related_lines": [401, 419], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "UserStatus", "description": "Enum type for user status"}, {"keyword": "status", "description": "Field name for user activation status"}, {"keyword": "=", "description": "Assignment operator"}, {"keyword": "UserStatus.ACTIVE", "description": "Default enum value"}, {"keyword": "?:", "description": "Ternary operator for null checking"}, {"keyword": "null", "description": "Null value check"}, {"keyword": "DEFAULT", "description": "Sets the default value"}]}, {"id": "E3.4", "sentence_id": 22, "sentence": "User.disabled DEFAULT_VALUE = false", "color": "#F97316", "related_lines": [403, 421, 1148], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "Boolean", "description": "Data type for true/false values"}, {"keyword": "disabled", "description": "Field name for account disable status"}, {"keyword": "=", "description": "Assignment operator"}, {"keyword": "false", "description": "Default boolean value - account enabled"}, {"keyword": "?:", "description": "Ternary operator for null checking"}, {"keyword": "null", "description": "Null value check"}, {"keyword": "BOOLEAN", "description": "Database column type"}, {"keyword": "DEFAULT false", "description": "Database default value"}]}, {"id": "E3.5", "sentence_id": 23, "sentence": "User.user_id must be unique", "color": "#F97316", "related_lines": [396, 414, 510, 514, 532, 919, 1141], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for user ID"}, {"keyword": "userId", "description": "Field that must be unique"}, {"keyword": "unique", "description": "Uniqueness constraint"}, {"keyword": "PRIMARY KEY", "description": "Database constraint ensuring uniqueness"}, {"keyword": "VARCHAR", "description": "Database column type"}]}, {"id": "E3.6", "sentence_id": 24, "sentence": "User.username must be unique", "color": "#F97316", "related_lines": [397, 415, 518, 522, 908, 914, 920, 1142], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for username"}, {"keyword": "username", "description": "Field that must be unique"}, {"keyword": "unique", "description": "Uniqueness constraint"}, {"keyword": "UNIQUE", "description": "Database constraint"}, {"keyword": "NOT NULL", "description": "Required field"}, {"keyword": "findByUsername", "description": "Method to find by unique username"}, {"keyword": "VARCHAR", "description": "Database column type"}]}, {"id": "E3.7", "sentence_id": 25, "sentence": "User.email must be unique and valid email format", "color": "#F97316", "related_lines": [398, 416, 526, 530, 921, 1143], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for email address"}, {"keyword": "email", "description": "Field that must be unique and formatted correctly"}, {"keyword": "unique", "description": "Uniqueness constraint"}, {"keyword": "UNIQUE", "description": "Database constraint"}, {"keyword": "NOT NULL", "description": "Required field"}, {"keyword": "VARCHAR", "description": "Database column type"}, {"keyword": "email format", "description": "Must follow email address format"}]}, {"id": "E3.8", "sentence_id": 26, "sentence": "User.status must be one of \"active\", \"inactive\",\"suspended\"", "color": "#F97316", "related_lines": [401, 419, 437, 469, 550, 554, 599, 600, 601, 602, 924, 1146], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "UserStatus", "description": "Enum type for user status"}, {"keyword": "status", "description": "Field that must match enum values"}, {"keyword": "enum", "description": "Java construct for fixed set of constants"}, {"keyword": "ACTIVE", "description": "Enum value for active users"}, {"keyword": "INACTIVE", "description": "Enum value for inactive users"}, {"keyword": "SUSPENDED", "description": "Enum value for suspended users"}, {"keyword": "VARCHAR", "description": "Database column type"}, {"keyword": "validation", "description": "Must validate against enum values"}]}]}, {"stack_name": "Role Entity", "steps": [{"id": "E4", "sentence_id": 27, "sentence": "Role has role_id^PK, name, description, inherits_from^FK, tenant_id^FK.", "color": "#10B981", "related_lines": [], "inner_components": [{"id": "A0", "sentence_id": 270000, "sentence": "Role has", "related_lines": [], "related_keywords": [], "clickable": false, "color": "#10B981"}, {"id": "A1", "sentence_id": 270001, "sentence": " role_id^PK,", "related_lines": [626, 635, 636, 648, 654, 655, 656, 684, 685, 686, 688, 689, 690, 954, 1156], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "roleId", "description": "Field name for role identifier"}, {"keyword": "PK", "description": "Primary Key - unique identifier"}, {"keyword": "getRoleId", "description": "Getter method for role ID"}, {"keyword": "setRoleId", "description": "Setter method for role ID"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "PRIMARY KEY", "description": "Database constraint ensuring uniqueness"}], "clickable": true, "color": "#10B981"}, {"id": "A2", "sentence_id": 270002, "sentence": " name,", "related_lines": [627, 635, 637, 649, 659, 660, 661, 692, 693, 694, 696, 697, 698, 955, 1157], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "name", "description": "Field name for role name"}, {"keyword": "getName", "description": "Getter method for role name"}, {"keyword": "setName", "description": "Setter method for role name"}, {"keyword": "VARCHAR", "description": "Database column type for text"}, {"keyword": "NOT NULL", "description": "Role name is required"}, {"keyword": "hasRole", "description": "Method to check if user has specific role name"}], "clickable": true, "color": "#10B981"}, {"id": "A3", "sentence_id": 270003, "sentence": " description,", "related_lines": [628, 635, 638, 650, 664, 665, 666, 700, 701, 702, 704, 705, 706, 956, 1158], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "description", "description": "Field name for role description"}, {"keyword": "getDescription", "description": "Getter method for description"}, {"keyword": "setDescription", "description": "Setter method for description"}, {"keyword": "TEXT", "description": "Database column type for longer text"}], "clickable": true, "color": "#10B981"}, {"id": "A4", "sentence_id": 270004, "sentence": " inherits_from^FK,", "related_lines": [629, 635, 639, 651, 669, 670, 671, 708, 709, 710, 712, 713, 714, 1159], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "Role", "description": "Self-referencing object type"}, {"keyword": "inheritsFrom", "description": "Field name for parent role reference"}, {"keyword": "FK", "description": "Foreign Key referencing another Role"}, {"keyword": "getInheritsFrom", "description": "Getter method for parent role"}, {"keyword": "setInheritsFrom", "description": "Setter method for parent role"}, {"keyword": "VARCHAR", "description": "Database column type"}, {"keyword": "REFERENCES role(role_id)", "description": "Foreign key constraint to same table"}, {"keyword": "self-referential", "description": "References another row in same table"}], "clickable": true, "color": "#10B981"}, {"id": "A5", "sentence_id": 270005, "sentence": " tenant_id^FK", "related_lines": [630, 635, 640, 652, 674, 675, 676, 716, 717, 718, 720, 721, 722, 957, 1160], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for storing text"}, {"keyword": "tenantId", "description": "Field name for tenant identifier"}, {"keyword": "FK", "description": "Foreign Key referencing Tenant"}, {"keyword": "getTenantId", "description": "Getter method for tenant ID"}, {"keyword": "setTenantId", "description": "Setter method for tenant ID"}, {"keyword": "VARCHAR", "description": "Database column type for text"}], "clickable": true, "color": "#10B981"}], "related_keywords": []}, {"id": "E4.1", "sentence_id": 28, "sentence": "Role has many-to-many relationship with User using UserRole junction table", "color": "#F97316", "related_lines": [406, 424, 493, 494, 495, 496, 590, 594, 595, 940, 941, 942, 1165, 1166, 1167, 1168, 1169], "related_keywords": [{"keyword": "List", "description": "Collection type for multiple roles/users"}, {"keyword": "Role", "description": "Entity type for user roles"}, {"keyword": "User", "description": "Entity type for system users"}, {"keyword": "many-to-many", "description": "Multiple roles can belong to multiple users"}, {"keyword": "user_role", "description": "Junction table name"}, {"keyword": "PRIMARY KEY (user_id, role_id)", "description": "Composite primary key in junction table"}, {"keyword": "REFERENCES", "description": "Foreign key constraints"}, {"keyword": "loadUserRoles", "description": "Method to load user's assigned roles"}, {"keyword": "hasRole", "description": "Method to check if user has specific role"}]}, {"id": "E4.2", "sentence_id": 29, "sentence": "Role has self-referential relationship using Role.inherits_from^FK to Role.role_id^PK", "color": "#F97316", "related_lines": [626, 629, 635, 636, 639, 651, 669, 708, 712, 959, 960, 961, 962, 963, 964, 1156, 1159], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "Role", "description": "Entity type that references itself"}, {"keyword": "roleId", "description": "Primary key field"}, {"keyword": "inheritsFrom", "description": "Self-referencing foreign key field"}, {"keyword": "self-referential", "description": "Relationship where table references itself"}, {"keyword": "PK", "description": "Primary Key"}, {"keyword": "FK", "description": "Foreign Key"}, {"keyword": "REFERENCES role(role_id)", "description": "Database constraint referencing same table"}, {"keyword": "parentRole", "description": "Variable for parent role object"}, {"keyword": "setRoleId", "description": "Set ID on parent role object"}]}, {"id": "E4.3", "sentence_id": 30, "sentence": "Role.role_id must be unique", "color": "#F97316", "related_lines": [626, 636, 648, 654, 684, 688, 954, 1156], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for role ID"}, {"keyword": "roleId", "description": "Field that must be unique"}, {"keyword": "unique", "description": "Uniqueness constraint"}, {"keyword": "PRIMARY KEY", "description": "Database constraint ensuring uniqueness"}, {"keyword": "VARCHAR", "description": "Database column type"}, {"keyword": "setRoleId", "description": "Method to set role ID"}]}, {"id": "E4.4", "sentence_id": 31, "sentence": "Role.name must be unique within tenant", "color": "#F97316", "related_lines": [627, 637, 649, 659, 692, 696, 955, 1157], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for role name"}, {"keyword": "name", "description": "Field that must be unique within tenant"}, {"keyword": "unique", "description": "Uniqueness constraint"}, {"keyword": "tenant", "description": "Scope for uniqueness constraint"}, {"keyword": "VARCHAR", "description": "Database column type"}, {"keyword": "NOT NULL", "description": "Role name is required"}, {"keyword": "composite", "description": "Uniqueness within tenant scope"}]}]}, {"stack_name": "BusinessRule BR001 for LeaveApplication:", "steps": [{"id": "B1", "sentence_id": 32, "sentence": "LeaveApplication.numDays > 3 must trigger HR Manager approval pathway", "color": "#10B981", "related_lines": [1036, 1037, 1038], "related_keywords": [{"keyword": "if", "description": "Check if"}, {"keyword": "getNumDays", "description": "Get the number of days"}, {"keyword": ">", "description": "Is greater than"}, {"keyword": "3", "description": "The number three"}, {"keyword": "throw", "description": "Show an error"}, {"keyword": "new", "description": "Create a new error message"}, {"keyword": "ValidationException", "description": "A type of error for when rules are broken"}]}, {"id": "B1.1", "sentence_id": 33, "sentence": "LeaveApplication.numDays <= 3 must trigger Manager approval pathway", "color": "#F97316", "related_lines": [1058, 1059, 1060], "related_keywords": [{"keyword": "if", "description": "Check if"}, {"keyword": "getNumDays", "description": "Get the number of days"}, {"keyword": "<=", "description": "Is less than or equal to"}, {"keyword": "3", "description": "The number three"}, {"keyword": "throw", "description": "Show an error"}, {"keyword": "new", "description": "Create a new error message"}, {"keyword": "ValidationException", "description": "A rule-breaking error"}]}, {"id": "B1.2", "sentence_id": 34, "sentence": "LeaveApplication.leaveType = \"Sick Leave\" and LeaveApplication.numDays > 3 must require medical certificate", "color": "#F97316", "related_lines": [29, 283, 284, 285, 24, 1036, 1037, 1038], "related_keywords": [{"keyword": "LeaveType", "description": "Enum type for leave categories"}, {"keyword": "SICK_LEAVE", "description": "Enum value for sick leave"}, {"keyword": "getLeaveType", "description": "Method to get the leave type"}, {"keyword": "==", "description": "Equality check"}, {"keyword": "&&", "description": "AND - both conditions must be true"}, {"keyword": "getNumDays", "description": "Method to get number of days"}, {"keyword": ">", "description": "Greater than comparison"}, {"keyword": "3", "description": "The threshold value"}, {"keyword": "medical certificate", "description": "Required documentation for sick leave > 3 days"}, {"keyword": "business rule", "description": "Custom validation logic"}]}]}, {"stack_name": "BusinessRule BR002 for LeaveApplication:", "steps": [{"id": "B2", "sentence_id": 35, "sentence": "LeaveApplication.remarks must be provided when LeaveApplication.status = \"Rejected\"", "color": "#10B981", "related_lines": [164, 165, 166], "related_keywords": [{"keyword": "if", "description": "Check if"}, {"keyword": "==", "description": "Is equal to"}, {"keyword": "LeaveStatus", "description": "The status type"}, {"keyword": "REJECTED", "description": "Leave was denied"}, {"keyword": "&&", "description": "AND - both must be true"}, {"keyword": "==", "description": "Is equal to"}, {"keyword": "null", "description": "No remarks provided"}, {"keyword": "||", "description": "OR"}, {"keyword": "trim", "description": "Remove extra spaces"}, {"keyword": "isEmpty", "description": "Nothing written"}, {"keyword": "throw", "description": "Show an error"}, {"keyword": "new", "description": "Create a new error message"}, {"keyword": "ValidationException", "description": "An error for when rules are broken"}]}, {"id": "B2.1", "sentence_id": 36, "sentence": "LeaveApplication.approvedBy must be captured when LeaveApplication.status is updated", "color": "#F97316", "related_lines": [28, 108, 237, 1042, 1064, 1041, 1063, 771, 799, 1221, 1224, 1225, 1232, 1233, 1240, 1241], "related_keywords": [{"keyword": "private", "description": "Field is accessible only within this class"}, {"keyword": "String", "description": "Data type for approver ID"}, {"keyword": "approvedBy", "description": "Field name for who approved/rejected"}, {"keyword": "setApprovedBy", "description": "Method to set the approver ID"}, {"keyword": "business rule", "description": "Must capture approver on status change"}, {"keyword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Method for manager approval"}, {"keyword": "hrManagerApproval", "description": "Method for HR manager approval"}, {"keyword": "MGR001", "description": "Example manager ID"}, {"keyword": "HR001", "description": "Example HR manager ID"}, {"keyword": "APPROVED", "description": "Status value for approved leave"}, {"keyword": "REJECTED", "description": "Status value for rejected leave"}]}]}, {"stack_name": "Global Objective: Leave Application Workflow", "steps": [{"id": "GO1", "sentence_id": 37, "sentence": "Workflow: LeaveApplicationWorkflow for LeaveApplication", "color": "#10B981", "related_lines": [986, 987, 989, 991, 1012, 1013, 1027, 1028, 1049, 1050], "related_keywords": [{"keyword": "LeaveApplicationService", "description": "Interface defining workflow operations"}, {"keyword": "LeaveApplicationServiceImpl", "description": "Implementation of the workflow service"}, {"keyword": "applyForLeave", "description": "Method for submitting leave application"}, {"keyword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Method for manager approval workflow"}, {"keyword": "hrManagerApproval", "description": "Method for HR manager approval workflow"}, {"keyword": "@Override", "description": "Annotation indicating interface implementation"}, {"keyword": "public", "description": "Methods are publicly accessible"}, {"keyword": "LeaveApplication", "description": "Return type for workflow methods"}, {"keyword": "workflow", "description": "Process orchestration for leave requests"}]}, {"id": "G01.1", "sentence_id": 38, "sentence": "States: Pending, Approved, Rejected", "color": "#F97316", "related_lines": [258, 259, 260, 261, 263, 264, 265, 266, 267, 268, 269, 270, 271], "related_keywords": [{"keyword": "enum", "description": "A list of fixed options"}, {"keyword": "LeaveStatus", "description": "The name for our status options"}, {"keyword": "PENDING", "description": "Waiting for approval"}, {"keyword": "APPROVED", "description": "Leave was approved"}, {"keyword": "REJECTED", "description": "Leave was denied"}, {"keyword": "final", "description": "This value cannot be changed"}, {"keyword": "String", "description": "Text"}, {"keyword": "displayName", "description": "Friendly name to show users"}]}, {"id": "G01.2", "sentence_id": 39, "sentence": "Transitions:\n    - Pending → Approved [Manager, HR Manager]", "color": "#F97316", "related_lines": [1040, 1221, 1222, 1223, 1224, 1225, 1226, 1062, 1229, 1230, 1231, 1232, 1233, 1234], "related_keywords": [{"keyword": "setStatus", "description": "Method to change the status"}, {"keyword": "LeaveStatus.APPROVED", "description": "The approved status value"}, {"keyword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Method for manager approval transition"}, {"keyword": "hrManagerApproval", "description": "Method for HR manager approval transition"}, {"keyword": "String", "description": "Parameters for ID, status, remarks, approver"}, {"keyword": "transition", "description": "State change from Pending to Approved"}]}, {"id": "G01.3", "sentence_id": 40, "sentence": "Transitions:\n    - Pending → Rejected [Manager, HR Manager]", "color": "#F97316", "related_lines": [1040, 1062, 1237, 1238, 1239, 1240, 1241, 1242], "related_keywords": [{"keyword": "setStatus", "description": "Method to change the status"}, {"keyword": "LeaveStatus.REJECTED", "description": "The rejected status value"}, {"keyword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Method for manager rejection transition"}, {"keyword": "hrManagerApproval", "description": "Method for HR manager rejection transition"}, {"keyword": "String", "description": "Parameters for remarks (required for rejection)"}, {"keyword": "transition", "description": "State change from Pending to Rejected"}, {"keyword": "validation", "description": "Remarks are required when rejecting"}]}, {"id": "G01.4", "sentence_id": 41, "sentence": "Actions: Apply, Approve, Reject", "color": "#F97316", "related_lines": [987, 989, 991, 1012, 1013, 1027, 1028, 1049, 1050, 1221, 1237], "related_keywords": [{"keyword": "applyForLeave", "description": "Action to submit new leave application"}, {"keyword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Action for manager to approve or reject"}, {"keyword": "hrManagerApproval", "description": "Action for HR manager to approve or reject"}, {"keyword": "LeaveStatus", "description": "Parameter to specify approve or reject"}, {"keyword": "interface", "description": "Contract defining available actions"}, {"keyword": "LeaveApplication", "description": "Return type for all actions"}, {"keyword": "SQLException", "description": "Exception type for database errors"}]}]}, {"stack_name": "Local Objective: ApplyForLeave", "steps": [{"id": "LO1", "sentence_id": 42, "sentence": "Employee has execution rights to apply for leave.", "color": "#5C6BC0", "related_lines": [987, 1012, 1013, 1014, 1015, 1016, 1018, 1020, 1022, 1024, 1198, 1199, 1200, 1201, 1202, 1203, 1204, 1206, 1207, 1208], "related_keywords": [{"keyword": "LeaveApplication", "description": "A leave request"}, {"keyword": "applyForLeave", "description": "The method name for applying"}, {"keyword": "throws", "description": "This method might create errors"}, {"keyword": "SQLException", "description": "Database-related errors"}, {"keyword": "@Override", "description": "This replaces a method from parent"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "LeaveApplication", "description": "Returns a leave request"}, {"keyword": "throws", "description": "Might have errors"}, {"keyword": "SQLException", "description": "Database errors"}, {"keyword": "if", "description": "Check if"}, {"keyword": "getLeaveId", "description": "Get the leave ID"}, {"keyword": "==", "description": "Is equal to"}, {"keyword": "null", "description": "No ID exists"}, {"keyword": "||", "description": "OR"}, {"keyword": "isEmpty", "description": "ID is empty"}, {"keyword": "setLeaveId", "description": "Set the leave ID"}, {"keyword": "generateLeaveId", "description": "Create a new ID"}, {"keyword": "calculateNumDays", "description": "Calculate how many days of leave"}, {"keyword": "setStatus", "description": "Set the status"}, {"keyword": "LeaveStatus", "description": "The status type"}, {"keyword": "PENDING", "description": "Set to waiting for approval"}, {"keyword": "validate", "description": "Check if everything is correct"}, {"keyword": "return", "description": "Give back"}, {"keyword": "leaveApplicationDAO", "description": "Database helper"}, {"keyword": "save", "description": "Save to database"}]}, {"id": "LO1.1", "sentence_id": 43, "sentence": "Inputs: LeaveApplication with leaveID*, employeeID*, startDate*, endDate*, numDays*, reason*, leaveType* (Annual Leave, Sick Leave, Parental Leave, Bereavement), leaveSubType [depends on: leaveType], status* (Pending, Approved, Rejected); Instructions with policyText [info].", "color": "#4CAF50", "related_lines": [20, 21, 22, 23, 24, 25, 26, 29, 30, 283, 284, 285, 286, 287, 258, 259, 260, 261], "related_keywords": [{"keyword": "LeaveApplication", "description": "Entity representing a leave request"}, {"keyword": "leaveId", "description": "Unique identifier for leave"}, {"keyword": "employeeId", "description": "ID of employee requesting leave"}, {"keyword": "startDate", "description": "When leave starts"}, {"keyword": "endDate", "description": "When leave ends"}, {"keyword": "numDays", "description": "Number of leave days (calculated)"}, {"keyword": "reason", "description": "Reason for leave"}, {"keyword": "leaveType", "description": "Category of leave"}, {"keyword": "leaveSubType", "description": "Specific sub-category"}, {"keyword": "status", "description": "Current approval status"}, {"keyword": "enum", "description": "Fixed set of values"}, {"keyword": "LeaveType", "description": "Leave category enum"}, {"keyword": "LeaveStatus", "description": "Status enum"}, {"keyword": "dependent", "description": "SubType depends on Type"}, {"keyword": "required", "description": "Mandatory fields marked with *"}]}, {"id": "LO1.2", "sentence_id": 44, "sentence": "System generates LeaveApplication.leaveID using generate_id with prefix \"LV\".", "color": "#FF9800", "related_lines": [1083, 1084, 1085, 1086, 1087], "related_keywords": [{"keyword": "DateTimeF<PERSON>atter", "description": "Tool for formatting dates"}, {"keyword": "ofPattern", "description": "Create a specific date format"}, {"keyword": "LocalDate", "description": "Today's date"}, {"keyword": "now", "description": "Right now"}, {"keyword": "format", "description": "Format the date as text"}, {"keyword": "String", "description": "Text"}, {"keyword": "format", "description": "Create formatted text"}, {"keyword": "Math", "description": "Math operations"}, {"keyword": "random", "description": "Generate random number"}, {"keyword": "return", "description": "Give back"}]}, {"id": "LO1.3", "sentence_id": 45, "sentence": "System calculates LeaveApplication.numDays using subtract_days with startDate and endDate. \n- Calculation logic: numDays = (endDate - startDate) + 1 \n- Logic implemented at: UI layer with backend validation  \n- Recalculated when: startDate or endDate changes", "color": "#FF9800", "related_lines": [129, 130, 131, 132, 133], "related_keywords": [{"keyword": "public", "description": "Anyone can use this"}, {"keyword": "void", "description": "Doesn't return anything"}, {"keyword": "if", "description": "Check if"}, {"keyword": "!=", "description": "Is not equal to"}, {"keyword": "null", "description": "Both dates exist"}, {"keyword": "&&", "description": "AND - both must be true"}, {"keyword": "=", "description": "Set equal to"}, {"keyword": "int", "description": "Convert to whole number"}, {"keyword": "ChronoUnit", "description": "Time calculation tool"}, {"keyword": "DAYS", "description": "Count in days"}, {"keyword": "between", "description": "Count days between two dates"}, {"keyword": "+", "description": "Add"}, {"keyword": "1", "description": "Add one day (to include both start and end days)"}]}, {"id": "LO1.4", "sentence_id": 46, "sentence": "System defaults LeaveApplication.status to \"Pending\".", "color": "#FF9800", "related_lines": [1020, 259], "related_keywords": [{"keyword": "setStatus", "description": "Method to change status"}, {"keyword": "LeaveStatus", "description": "Enum for status values"}, {"keyword": "PENDING", "description": "Default status for new applications"}, {"keyword": "default", "description": "Auto-assigned initial value"}]}, {"id": "LO1.5", "sentence_id": 47, "sentence": "System populates LeaveApplication.leaveSubType [depends on: leaveType] using fetch_filtered_records from LeaveSubType where LeaveSubType.leaveType = LeaveApplication.leaveType and LeaveSubType.active = true.", "color": "#FF9800", "related_lines": [879, 880, 885, 1077, 1078, 1079], "related_keywords": [{"keyword": "getLeaveSubTypesByLeaveType", "description": "Service method to fetch sub-types"}, {"keyword": "String", "description": "Leave type parameter"}, {"keyword": "findByLeaveTypeAndActiveTrue", "description": "DAO method for filtered query"}, {"keyword": "PreparedStatement", "description": "SQL query preparation"}, {"keyword": "WHERE", "description": "SQL filter clause"}, {"keyword": "leave_type = ?", "description": "Filter by leave type"}, {"keyword": "AND active = true", "description": "Filter for active sub-types only"}, {"keyword": "ResultSet", "description": "Database query results"}, {"keyword": "List", "description": "Collection of matching sub-types"}]}, {"id": "LO1.6", "sentence_id": 48, "sentence": "System displays Instructions.policyText [info] using to_uppercase with \"Please specify your leave dates and reason. If selecting Sick Leave for more than 3 days, a medical certificate will be required.\"", "color": "#FF9800", "related_lines": [], "related_keywords": [{"keyword": "policyText", "description": "Field for displaying policy instructions"}, {"keyword": "to_uppercase", "description": "Text transformation method"}, {"keyword": "info", "description": "Type qualifier for informational text"}, {"keyword": "UI display", "description": "Front-end presentation logic"}, {"keyword": "medical certificate", "description": "Policy requirement for sick leave > 3 days"}]}, {"id": "LO1.7", "sentence_id": 49, "sentence": "Outputs: LeaveApplication with leaveID, employeeID, startDate, endDate, numDays, reason, leaveType, leaveSubType, status.", "color": "#2196F3", "related_lines": [1024, 1206, 1207, 1208], "related_keywords": [{"keyword": "return", "description": "Return statement for the method"}, {"keyword": "leaveApplicationDAO.save", "description": "Database save operation"}, {"keyword": "LeaveApplication", "description": "Return type with all populated fields"}, {"keyword": "savedApplication", "description": "Variable holding the saved application"}, {"keyword": "System.out.println", "description": "Console output for verification"}, {"keyword": "getLeaveId", "description": "Get the generated ID"}, {"keyword": "getNumDays", "description": "Get calculated days"}]}, {"id": "LO1.8", "sentence_id": 50, "sentence": "LeaveApplication.leaveID displays as oj-input-text with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-text", "description": "Oracle JET text input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "UI component", "description": "Front-end element specification"}, {"keyword": "oracle jet", "description": "Frontend framework being used"}, {"keyword": "display", "description": "How data is presented to user"}]}, {"id": "LO1.9", "sentence_id": 51, "sentence": "LeaveApplication.employeeID displays as oj-input-text.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-text", "description": "Oracle JET text input component"}, {"keyword": "employeeID", "description": "Field for employee identifier"}, {"keyword": "editable", "description": "User can enter/modify value"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO1.10", "sentence_id": 52, "sentence": "LeaveApplication.startDate displays as oj-input-date.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-date", "description": "Oracle JET date picker component"}, {"keyword": "startDate", "description": "Field for leave start date"}, {"keyword": "date picker", "description": "Calendar selection widget"}, {"keyword": "LocalDate", "description": "Java date type for this field"}]}, {"id": "LO1.11", "sentence_id": 53, "sentence": "LeaveApplication.endDate displays as oj-input-date.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-date", "description": "Oracle JET date picker component"}, {"keyword": "endDate", "description": "Field for leave end date"}, {"keyword": "date picker", "description": "Calendar selection widget"}, {"keyword": "LocalDate", "description": "Java date type for this field"}]}, {"id": "LO1.12", "sentence_id": 54, "sentence": "LeaveApplication.numDays displays as oj-input-number with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-number", "description": "Oracle JET number input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "numDays", "description": "Calculated field for number of days"}, {"keyword": "derived", "description": "Value calculated from other fields"}, {"keyword": "Integer", "description": "Java number type for this field"}]}, {"id": "LO1.13", "sentence_id": 55, "sentence": "LeaveApplication.reason displays as oj-text-area.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-text-area", "description": "Oracle JET multi-line text input component"}, {"keyword": "reason", "description": "Field for leave request explanation"}, {"keyword": "multi-line", "description": "Allows multiple lines of text"}, {"keyword": "String", "description": "Java text type for this field"}]}, {"id": "LO1.14", "sentence_id": 56, "sentence": "LeaveApplication.leaveType displays as oj-combobox-one with source from entity enumeration.", "color": "#9C27B0", "related_lines": [283, 284, 285, 286, 287], "related_keywords": [{"keyword": "oj-combobox-one", "description": "Oracle JET single-select dropdown component"}, {"keyword": "leaveType", "description": "Field for leave category selection"}, {"keyword": "enum", "description": "Source of dropdown options"}, {"keyword": "LeaveType", "description": "Enum with leave type values"}, {"keyword": "single-select", "description": "User can select one option"}, {"keyword": "ANNUAL_LEAVE", "description": "Option in dropdown"}, {"keyword": "SICK_LEAVE", "description": "Option in dropdown"}, {"keyword": "PARENTAL_LEAVE", "description": "Option in dropdown"}, {"keyword": "BEREAVEMENT", "description": "Option in dropdown"}]}, {"id": "LO1.15", "sentence_id": 57, "sentence": "LeaveApplication.leaveSubType [depends on: leaveType] displays as oj-combobox-one with source from LeaveSubType filtered by LeaveApplication.leaveType.", "color": "#9C27B0", "related_lines": [879, 880, 885, 893, 894], "related_keywords": [{"keyword": "oj-combobox-one", "description": "Oracle JET single-select dropdown component"}, {"keyword": "leaveSubType", "description": "Field for specific leave sub-category"}, {"keyword": "dependent", "description": "Options depend on selected leaveType"}, {"keyword": "filtered", "description": "Data filtered based on main type"}, {"keyword": "findByLeaveTypeAndActiveTrue", "description": "Query method for sub-types"}, {"keyword": "AND active = true", "description": "Only show active sub-types"}, {"keyword": "dynamic", "description": "Options change based on parent selection"}]}, {"id": "LO1.16", "sentence_id": 58, "sentence": "Instructions.policyText [info] displays as oj-text.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-text", "description": "Oracle JET text display component"}, {"keyword": "policyText", "description": "Field for policy instructions"}, {"keyword": "info", "description": "Type qualifier for informational content"}, {"keyword": "read-only", "description": "Display-only, no user input"}, {"keyword": "instructions", "description": "Guidance text for users"}]}, {"id": "LO1.17", "sentence_id": 59, "sentence": "Validations: LeaveApplication.leaveID must be unique. Error message: \"Leave ID is required and must be unique\"", "color": "#E91E63", "related_lines": [136, 137, 138], "related_keywords": [{"keyword": "validate", "description": "Method containing validation logic"}, {"keyword": "if", "description": "Conditional check"}, {"keyword": "==", "description": "Equality comparison"}, {"keyword": "null", "description": "Check for missing value"}, {"keyword": "trim", "description": "Remove whitespace"}, {"keyword": "isEmpty", "description": "Check for empty string"}, {"keyword": "throw", "description": "Throw exception on validation failure"}, {"keyword": "ValidationException", "description": "Custom exception for validation errors"}, {"keyword": "unique", "description": "Uniqueness constraint"}]}, {"id": "LO1.18", "sentence_id": 60, "sentence": "LeaveApplication.employeeID must be provided. Error message: \"Employee ID is required\"", "color": "#E91E63", "related_lines": [140, 141, 142], "related_keywords": [{"keyword": "validate", "description": "Method containing validation logic"}, {"keyword": "if", "description": "Conditional check"}, {"keyword": "==", "description": "Equality comparison"}, {"keyword": "null", "description": "Check for missing value"}, {"keyword": "trim", "description": "Remove whitespace"}, {"keyword": "isEmpty", "description": "Check for empty string"}, {"keyword": "throw", "description": "Throw exception on validation failure"}, {"keyword": "ValidationException", "description": "Custom exception for validation errors"}, {"keyword": "required", "description": "Mandatory field validation"}]}, {"id": "LO1.19", "sentence_id": 61, "sentence": "LeaveApplication.startDate must be a valid date. Error message: \"Start date is required\"", "color": "#E91E63", "related_lines": [144, 145, 146], "related_keywords": [{"keyword": "validate", "description": "Method containing validation logic"}, {"keyword": "if", "description": "Conditional check"}, {"keyword": "==", "description": "Equality comparison"}, {"keyword": "null", "description": "Check for missing date"}, {"keyword": "throw", "description": "Throw exception on validation failure"}, {"keyword": "ValidationException", "description": "Custom exception for validation errors"}, {"keyword": "LocalDate", "description": "Java date type"}, {"keyword": "required", "description": "Mandatory field validation"}]}, {"id": "LO1.20", "sentence_id": 62, "sentence": "LeaveApplication.endDate must be after startDate. Error message: \"End date must be after start date\"", "color": "#E91E63", "related_lines": [152, 153, 154], "related_keywords": [{"keyword": "validate", "description": "Method containing validation logic"}, {"keyword": "if", "description": "Conditional check"}, {"keyword": "isBefore", "description": "Date comparison method"}, {"keyword": "throw", "description": "Throw exception on validation failure"}, {"keyword": "ValidationException", "description": "Custom exception for validation errors"}, {"keyword": "LocalDate", "description": "Java date type"}, {"keyword": "date range", "description": "Validating date sequence"}]}, {"id": "LO1.21", "sentence_id": 63, "sentence": "LeaveApplication.reason must be provided with minimum 10 characters. Error message: \"Please provide a detailed reason for your leave request\"", "color": "#E91E63", "related_lines": [156, 157, 158], "related_keywords": [{"keyword": "validate", "description": "Method containing validation logic"}, {"keyword": "if", "description": "Conditional check"}, {"keyword": "==", "description": "Equality comparison"}, {"keyword": "null", "description": "Check for missing value"}, {"keyword": "trim", "description": "Remove whitespace"}, {"keyword": "length", "description": "Get string length"}, {"keyword": "<", "description": "Less than comparison"}, {"keyword": "10", "description": "Minimum character count"}, {"keyword": "throw", "description": "Throw exception on validation failure"}, {"keyword": "ValidationException", "description": "Custom exception for validation errors"}]}, {"id": "LO1.22", "sentence_id": 64, "sentence": "LeaveApplication.leaveType must be one of \"Annual Leave\", \"Sick Leave\", \"Parental Leave\", \"Bereavement\". Error message: \"Please select a valid leave type\"", "color": "#E91E63", "related_lines": [160, 161, 162, 283, 284, 285, 286, 287], "related_keywords": [{"keyword": "validate", "description": "Method containing validation logic"}, {"keyword": "if", "description": "Conditional check"}, {"keyword": "==", "description": "Equality comparison"}, {"keyword": "null", "description": "Check for missing value"}, {"keyword": "throw", "description": "Throw exception on validation failure"}, {"keyword": "ValidationException", "description": "Custom exception for validation errors"}, {"keyword": "enum", "description": "Enumeration validation"}, {"keyword": "LeaveType", "description": "Leave type enum"}, {"keyword": "ANNUAL_LEAVE", "description": "Valid option"}, {"keyword": "SICK_LEAVE", "description": "Valid option"}, {"keyword": "PARENTAL_LEAVE", "description": "Valid option"}, {"keyword": "BEREAVEMENT", "description": "Valid option"}]}, {"id": "LO1.23", "sentence_id": 65, "sentence": "When LeaveApplication.numDays > 3, route to HRManagerApproval.", "color": "#795548", "related_lines": [1036, 1037, 1038], "related_keywords": [{"keyword": "if", "description": "Conditional routing logic"}, {"keyword": "getNumDays", "description": "Get number of leave days"}, {"keyword": ">", "description": "Greater than comparison"}, {"keyword": "3", "description": "<PERSON><PERSON><PERSON><PERSON> for routing decision"}, {"keyword": "throw", "description": "Exception to enforce routing"}, {"keyword": "ValidationException", "description": "Custom exception"}, {"keyword": "HR Manager", "description": "Role responsible for approval"}, {"keyword": "routing", "description": "Workflow decision logic"}]}, {"id": "LO1.24", "sentence_id": 66, "sentence": "When LeaveApplication.numDays <= 3, route to ManagerApproval.", "color": "#795548", "related_lines": [1058, 1059, 1060], "related_keywords": [{"keyword": "if", "description": "Conditional routing logic"}, {"keyword": "getNumDays", "description": "Get number of leave days"}, {"keyword": "<=", "description": "Less than or equal comparison"}, {"keyword": "3", "description": "<PERSON><PERSON><PERSON><PERSON> for routing decision"}, {"keyword": "throw", "description": "Exception to enforce routing"}, {"keyword": "ValidationException", "description": "Custom exception"}, {"keyword": "Manager", "description": "Role responsible for approval"}, {"keyword": "routing", "description": "Workflow decision logic"}]}]}, {"stack_name": "Local Objective: ManagerApproval", "steps": [{"id": "LO2", "sentence_id": 67, "sentence": "Manager has execution and update rights", "color": "#5C6BC0", "related_lines": [989, 990, 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1034, 1036, 1037, 1038, 1040, 1041, 1042, 1044, 1046, 1221, 1222, 1223, 1224, 1225, 1226, 1227], "related_keywords": [{"keyword": "@Override", "description": "Replaces parent method"}, {"keyword": "public", "description": "Anyone can use this"}, {"keyword": "LeaveApplication", "description": "Returns a leave request"}, {"keyword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Method name for manager approval"}, {"keyword": "String", "description": "Text parameter"}, {"keyword": "LeaveStatus", "description": "Status parameter"}, {"keyword": "throws", "description": "Might have errors"}, {"keyword": "SQLException", "description": "Database errors"}, {"keyword": "LeaveApplication", "description": "Leave request"}, {"keyword": "=", "description": "Set equal to"}, {"keyword": "getLeaveApplicationById", "description": "Find leave by ID"}, {"keyword": "if", "description": "Check if"}, {"keyword": "==", "description": "Is equal to"}, {"keyword": "null", "description": "Not found"}, {"keyword": "throw", "description": "Show error"}, {"keyword": "new", "description": "Create new error"}, {"keyword": "ValidationException", "description": "Rule violation error"}, {"keyword": "setStatus", "description": "Change status"}, {"keyword": "setRemarks", "description": "Add comments"}, {"keyword": "setApprovedBy", "description": "Record who approved"}, {"keyword": "validate", "description": "Check if everything is correct"}, {"keyword": "return", "description": "Give back"}, {"keyword": "leaveApplicationDAO", "description": "Database helper"}, {"keyword": "save", "description": "Save to database"}]}, {"id": "LO2.1", "sentence_id": 68, "sentence": "HR Manager has read rights", "color": "#5C6BC0", "related_lines": [1030, 1031, 1032, 1033, 1034], "related_keywords": [{"keyword": "getLeaveApplicationById", "description": "Method to retrieve leave application"}, {"keyword": "public", "description": "Accessible by roles with permissions"}, {"keyword": "LeaveApplication", "description": "Return type for read operation"}, {"keyword": "Optional", "description": "Wrapper for potentially missing data"}, {"keyword": "findById", "description": "DAO method for record retrieval"}, {"keyword": "read-only", "description": "HR can view but not modify in Manager workflow"}]}, {"id": "LO2.2", "sentence_id": 69, "sentence": "Inputs: LeaveApplication with leaveID*, employeeID*, startDate*, endDate*, numDays*, reason*, leaveType*, leaveSubType, status* (Pending, Approved, Rejected); Instructions with policyText [info]", "color": "#4CAF50", "related_lines": [1028, 1029, 1030, 1031], "related_keywords": [{"keyword": "String", "description": "leaveId parameter"}, {"keyword": "LeaveStatus", "description": "status parameter"}, {"keyword": "String", "description": "remarks parameter"}, {"keyword": "String", "description": "approvedBy parameter"}, {"keyword": "getLeaveApplicationById", "description": "Fetch existing application"}, {"keyword": "PENDING", "description": "Expected initial status"}, {"keyword": "required", "description": "Mandatory input fields marked with *"}, {"keyword": "readonly", "description": "Some fields cannot be modified"}]}, {"id": "LO2.3", "sentence_id": 70, "sentence": "System displays Instructions.policyText [info] using to_uppercase with \"This leave request is for 3 days or less and requires your approval. Please review the details carefully.\"", "color": "#FF9800", "related_lines": [], "related_keywords": [{"keyword": "policyText", "description": "Field for displaying policy instructions"}, {"keyword": "to_uppercase", "description": "Text transformation method"}, {"keyword": "info", "description": "Type qualifier for informational text"}, {"keyword": "3 days or less", "description": "Manager approval threshold"}, {"keyword": "UI display", "description": "Front-end presentation logic"}]}, {"id": "LO2.4", "sentence_id": 71, "sentence": "Manager updates LeaveApplication.status to \"Approved\" or \"Rejected\".", "color": "#FF9800", "related_lines": [1040], "related_keywords": [{"keyword": "setStatus", "description": "Method to update status"}, {"keyword": "status", "description": "Parameter containing new status"}, {"keyword": "LeaveStatus", "description": "Enum type for status values"}, {"keyword": "APPROVED", "description": "Possible status value"}, {"keyword": "REJECTED", "description": "Possible status value"}, {"keyword": "update", "description": "Modify existing data"}]}, {"id": "LO2.5", "sentence_id": 72, "sentence": "Manager provides LeaveApplication.remarks [optional, required when status=\"Rejected\"].", "color": "#FF9800", "related_lines": [1041, 164, 165, 166], "related_keywords": [{"keyword": "setRemarks", "description": "Method to add comments"}, {"keyword": "remarks", "description": "Parameter for comments"}, {"keyword": "String", "description": "Text type for remarks"}, {"keyword": "conditional", "description": "Required based on status"}, {"keyword": "REJECTED", "description": "Status requiring remarks"}, {"keyword": "validate", "description": "Validation checks remarks when rejected"}, {"keyword": "ValidationException", "description": "Error if remarks missing on rejection"}]}, {"id": "LO2.6", "sentence_id": 73, "sentence": "System captures LeaveApplication.approvedBy using current_timestamp.", "color": "#FF9800", "related_lines": [1042], "related_keywords": [{"keyword": "setApprovedBy", "description": "Method to record approver"}, {"keyword": "approvedBy", "description": "Parameter with manager ID"}, {"keyword": "String", "description": "Type for approver identifier"}, {"keyword": "audit", "description": "Tracking who made the decision"}, {"keyword": "timestamp", "description": "When decision was made"}]}, {"id": "LO2.7", "sentence_id": 74, "sentence": "Outputs: LeaveApplication with leaveID, status, remarks, approvedBy", "color": "#2196F3", "related_lines": [1046, 1227], "related_keywords": [{"keyword": "return", "description": "Return statement for the method"}, {"keyword": "leaveApplicationDAO.save", "description": "Database save operation"}, {"keyword": "LeaveApplication", "description": "Return type with updated fields"}, {"keyword": "approvedShortLeave", "description": "Variable holding approved application"}, {"keyword": "System.out.println", "description": "Console output for verification"}, {"keyword": "getStatus", "description": "Get updated status"}]}, {"id": "LO2.8", "sentence_id": 75, "sentence": "LeaveApplication.leaveID displays as oj-input-text with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-text", "description": "Oracle JET text input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "leaveID", "description": "Field for leave identifier"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.9", "sentence_id": 76, "sentence": "LeaveApplication.employeeID displays as oj-input-text with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-text", "description": "Oracle JET text input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "employeeID", "description": "Field for employee identifier"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.10", "sentence_id": 77, "sentence": "LeaveApplication.startDate displays as oj-input-date with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-date", "description": "Oracle JET date input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "startDate", "description": "Field for leave start date"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.11", "sentence_id": 78, "sentence": "LeaveApplication.endDate displays as oj-input-date with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-date", "description": "Oracle JET date input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "endDate", "description": "Field for leave end date"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.12", "sentence_id": 79, "sentence": "LeaveApplication.numDays displays as oj-input-number with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-number", "description": "Oracle JET number input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "numDays", "description": "Field for number of leave days"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.13", "sentence_id": 80, "sentence": "LeaveApplication.reason displays as oj-text-area with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-text-area", "description": "Oracle JET multi-line text component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "reason", "description": "Field for leave request explanation"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.14", "sentence_id": 81, "sentence": "LeaveApplication.leaveType displays as oj-input-text with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-text", "description": "Oracle JET text input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "leaveType", "description": "Field for leave category"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.15", "sentence_id": 82, "sentence": "LeaveApplication.leaveSubType displays as oj-input-text with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-text", "description": "Oracle JET text input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "leaveSubType", "description": "Field for leave sub-category"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.16", "sentence_id": 83, "sentence": "Instructions.policyText [info] displays as oj-text.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-text", "description": "Oracle JET text display component"}, {"keyword": "policyText", "description": "Field for policy instructions"}, {"keyword": "info", "description": "Type qualifier for informational content"}, {"keyword": "read-only", "description": "Display-only, no user input"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.17", "sentence_id": 84, "sentence": "LeaveApplication.status displays as oj-select-single with options \"Approved\" and \"Rejected\".", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-select-single", "description": "Oracle JET single-select dropdown"}, {"keyword": "status", "description": "Field for approval decision"}, {"keyword": "APPROVED", "description": "Option for approving leave"}, {"keyword": "REJECTED", "description": "Option for rejecting leave"}, {"keyword": "options", "description": "Limited to two choices"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.18", "sentence_id": 85, "sentence": "LeaveApplication.remarks displays as oj-text-area.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-text-area", "description": "Oracle JET multi-line text input component"}, {"keyword": "remarks", "description": "Field for approval/rejection comments"}, {"keyword": "multi-line", "description": "Allows multiple lines of text"}, {"keyword": "editable", "description": "Manager can input comments"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO2.19", "sentence_id": 86, "sentence": "LeaveApplication.leaveID must exist. Error message: \"Invalid leave application reference\"", "color": "#E91E63", "related_lines": [1032, 1033, 1034], "related_keywords": [{"keyword": "getLeaveApplicationById", "description": "Method to find application"}, {"keyword": "if", "description": "Conditional check"}, {"keyword": "==", "description": "Equality comparison"}, {"keyword": "null", "description": "Check for non-existent record"}, {"keyword": "throw", "description": "Throw exception on validation failure"}, {"keyword": "ValidationException", "description": "Custom exception for validation errors"}, {"keyword": "exists", "description": "Must be found in database"}]}, {"id": "LO2.20", "sentence_id": 87, "sentence": "LeaveApplication.status must be one of \"Pending\", \"Approved\", \"Rejected\". Error message: \"Please select a valid approval status\"", "color": "#E91E63", "related_lines": [1040, 258, 259, 260, 261], "related_keywords": [{"keyword": "LeaveStatus", "description": "Enum type for status validation"}, {"keyword": "setStatus", "description": "Method to set status"}, {"keyword": "PENDING", "description": "Valid initial status"}, {"keyword": "APPROVED", "description": "Valid status option"}, {"keyword": "REJECTED", "description": "Valid status option"}, {"keyword": "enum", "description": "Enumeration validation"}, {"keyword": "valid options", "description": "Limited set of allowed values"}]}, {"id": "LO2.21", "sentence_id": 88, "sentence": "LeaveApplication.remarks must be provided when status is \"Rejected\". Error message: \"Please provide a reason for rejection\"", "color": "#E91E63", "related_lines": [164, 165, 166], "related_keywords": [{"keyword": "validate", "description": "Method containing validation logic"}, {"keyword": "if", "description": "Conditional check"}, {"keyword": "==", "description": "Equality comparison"}, {"keyword": "LeaveStatus.REJECTED", "description": "Status requiring remarks"}, {"keyword": "&&", "description": "AND condition"}, {"keyword": "==", "description": "Second equality check"}, {"keyword": "null", "description": "Check for missing remarks"}, {"keyword": "||", "description": "OR condition"}, {"keyword": "trim", "description": "Remove whitespace"}, {"keyword": "isEmpty", "description": "Check for empty string"}, {"keyword": "throw", "description": "Throw exception on validation failure"}, {"keyword": "ValidationException", "description": "Custom exception for validation errors"}]}, {"id": "LO2.22", "sentence_id": 89, "sentence": "Terminal - no further routing", "color": "#795548", "related_lines": [1046], "related_keywords": [{"keyword": "return", "description": "End of workflow method"}, {"keyword": "terminal", "description": "No additional processing required"}, {"keyword": "save", "description": "Final database operation"}, {"keyword": "workflow", "description": "Process ends here"}, {"keyword": "complete", "description": "No further steps needed"}]}]}, {"stack_name": "Local Objective: HRManagerApproval", "steps": [{"id": "LO3", "sentence_id": 90, "sentence": "HR Manager has execution and update rights", "color": "#5C6BC0", "related_lines": [992, 993, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1058, 1059, 1060, 1062, 1063, 1064, 1066, 1068, 1229, 1230, 1231, 1232, 1233, 1234, 1235, 1237, 1238, 1239, 1240, 1241, 1242, 1243, 1244], "related_keywords": [{"keyword": "@Override", "description": "Interface method implementation"}, {"keyword": "public", "description": "Publicly accessible method"}, {"keyword": "LeaveApplication", "description": "Return type for the method"}, {"keyword": "hrManagerApproval", "description": "Method name for HR manager approval"}, {"keyword": "String", "description": "Parameters for IDs, remarks, approver"}, {"keyword": "LeaveStatus", "description": "Status parameter"}, {"keyword": "throws", "description": "Exception declaration"}, {"keyword": "SQLException", "description": "Database-related exceptions"}, {"keyword": "getLeaveApplicationById", "description": "Retrieve existing application"}, {"keyword": "ValidationException", "description": "Custom validation exception"}, {"keyword": "setStatus", "description": "Update application status"}, {"keyword": "setRemarks", "description": "Add approval/rejection remarks"}, {"keyword": "setApprovedBy", "description": "Record who approved"}, {"keyword": "validate", "description": "Validate application data"}, {"keyword": "save", "description": "Persist changes to database"}]}, {"id": "LO3.1", "sentence_id": 91, "sentence": "CEO and CFO have read rights", "color": "#5C6BC0", "related_lines": [1052, 1053, 1054, 1055, 1056], "related_keywords": [{"keyword": "getLeaveApplicationById", "description": "Method to retrieve leave application"}, {"keyword": "public", "description": "Accessible by roles with permissions"}, {"keyword": "LeaveApplication", "description": "Return type for read operation"}, {"keyword": "Optional", "description": "Wrapper for potentially missing data"}, {"keyword": "findById", "description": "DAO method for record retrieval"}, {"keyword": "read-only", "description": "Executive roles can view but not modify"}, {"keyword": "executive", "description": "High-level organizational roles"}]}, {"id": "LO3.2", "sentence_id": 92, "sentence": "Inputs: LeaveApplication with leaveID*, employeeID*, startDate*, endDate*, numDays*, reason*, leaveType*, leaveSubType, status* (Pending, Approved, Rejected); Instructions with policyText [info]", "color": "#4CAF50", "related_lines": [1050, 1051, 1052, 1053], "related_keywords": [{"keyword": "String", "description": "leaveId parameter"}, {"keyword": "LeaveStatus", "description": "status parameter for approval decision"}, {"keyword": "String", "description": "remarks parameter"}, {"keyword": "String", "description": "approvedBy parameter (HR ID)"}, {"keyword": "getLeaveApplicationById", "description": "Fetch existing application"}, {"keyword": "PENDING", "description": "Expected initial status"}, {"keyword": "required", "description": "Mandatory input fields marked with *"}, {"keyword": "readonly", "description": "Some fields cannot be modified"}]}, {"id": "LO3.3", "sentence_id": 93, "sentence": "System displays Instructions.policyText [info] using to_uppercase with \"This leave request is for more than 3 days and requires HR approval. Please verify the employee's leave balance before approval.\"", "color": "#FF9800", "related_lines": [], "related_keywords": [{"keyword": "policyText", "description": "Field for displaying policy instructions"}, {"keyword": "to_uppercase", "description": "Text transformation method"}, {"keyword": "info", "description": "Type qualifier for informational text"}, {"keyword": "more than 3 days", "description": "HR approval threshold"}, {"keyword": "leave balance", "description": "Additional verification required"}, {"keyword": "UI display", "description": "Front-end presentation logic"}]}, {"id": "LO3.4", "sentence_id": 94, "sentence": "HR Manager updates LeaveApplication.status to \"Approved\" or \"Rejected\".", "color": "#FF9800", "related_lines": [1062], "related_keywords": [{"keyword": "setStatus", "description": "Method to update status"}, {"keyword": "status", "description": "Parameter containing new status"}, {"keyword": "LeaveStatus", "description": "Enum type for status values"}, {"keyword": "APPROVED", "description": "Possible status value"}, {"keyword": "REJECTED", "description": "Possible status value"}, {"keyword": "update", "description": "Modify existing data"}]}, {"id": "LO3.5", "sentence_id": 95, "sentence": "HR Manager provides LeaveApplication.remarks [optional, required when status=\"Rejected\"].", "color": "#FF9800", "related_lines": [1063, 164, 165, 166], "related_keywords": [{"keyword": "setRemarks", "description": "Method to add comments"}, {"keyword": "remarks", "description": "Parameter for comments"}, {"keyword": "String", "description": "Text type for remarks"}, {"keyword": "conditional", "description": "Required based on status"}, {"keyword": "REJECTED", "description": "Status requiring remarks"}, {"keyword": "validate", "description": "Validation checks remarks when rejected"}, {"keyword": "ValidationException", "description": "Error if remarks missing on rejection"}]}, {"id": "LO3.6", "sentence_id": 96, "sentence": "System captures LeaveApplication.approvedBy using current_timestamp.", "color": "#FF9800", "related_lines": [1064], "related_keywords": [{"keyword": "setApprovedBy", "description": "Method to record approver"}, {"keyword": "approvedBy", "description": "Parameter with HR manager ID"}, {"keyword": "String", "description": "Type for approver identifier"}, {"keyword": "audit", "description": "Tracking who made the decision"}, {"keyword": "timestamp", "description": "When decision was made"}, {"keyword": "HR ID", "description": "HR manager identifier"}]}, {"id": "LO3.7", "sentence_id": 97, "sentence": "Outputs: LeaveApplication with leaveID, status, remarks, approvedBy", "color": "#2196F3", "related_lines": [1068, 1235, 1243, 1244], "related_keywords": [{"keyword": "return", "description": "Return statement for the method"}, {"keyword": "leaveApplicationDAO.save", "description": "Database save operation"}, {"keyword": "LeaveApplication", "description": "Return type with updated fields"}, {"keyword": "approvedLongLeave", "description": "Variable holding approved application"}, {"keyword": "rejectedLeave", "description": "Variable holding rejected application"}, {"keyword": "System.out.println", "description": "Console output for verification"}, {"keyword": "getStatus", "description": "Get updated status"}, {"keyword": "getRemarks", "description": "Get rejection reason"}]}, {"id": "LO3.8", "sentence_id": 98, "sentence": "LeaveApplication.leaveID displays as oj-input-text with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-text", "description": "Oracle JET text input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "leaveID", "description": "Field for leave identifier"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.9", "sentence_id": 99, "sentence": "LeaveApplication.employeeID displays as oj-input-text with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-text", "description": "Oracle JET text input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "employeeID", "description": "Field for employee identifier"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.10", "sentence_id": 100, "sentence": "LeaveApplication.startDate displays as oj-input-date with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-date", "description": "Oracle JET date input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "startDate", "description": "Field for leave start date"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.11", "sentence_id": 101, "sentence": "LeaveApplication.endDate displays as oj-input-date with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-date", "description": "Oracle JET date input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "endDate", "description": "Field for leave end date"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.12", "sentence_id": 102, "sentence": "LeaveApplication.numDays displays as oj-input-number with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-number", "description": "Oracle JET number input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "numDays", "description": "Field for number of leave days"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.13", "sentence_id": 103, "sentence": "LeaveApplication.reason displays as oj-text-area with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-text-area", "description": "Oracle JET multi-line text component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "reason", "description": "Field for leave request explanation"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.14", "sentence_id": 104, "sentence": "LeaveApplication.leaveType displays as oj-input-text with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-text", "description": "Oracle JET text input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "leaveType", "description": "Field for leave category"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.15", "sentence_id": 105, "sentence": "LeaveApplication.leaveSubType displays as oj-input-text with readonly property.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-input-text", "description": "Oracle JET text input component"}, {"keyword": "readonly", "description": "Property preventing user edits"}, {"keyword": "leaveSubType", "description": "Field for leave sub-category"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.16", "sentence_id": 106, "sentence": "Instructions.policyText [info] displays as oj-text.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-text", "description": "Oracle JET text display component"}, {"keyword": "policyText", "description": "Field for policy instructions"}, {"keyword": "info", "description": "Type qualifier for informational content"}, {"keyword": "read-only", "description": "Display-only, no user input"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.17", "sentence_id": 107, "sentence": "LeaveApplication.status displays as oj-select-single with options \"Approved\" and \"Rejected\".", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-select-single", "description": "Oracle JET single-select dropdown"}, {"keyword": "status", "description": "Field for approval decision"}, {"keyword": "APPROVED", "description": "Option for approving leave"}, {"keyword": "REJECTED", "description": "Option for rejecting leave"}, {"keyword": "options", "description": "Limited to two choices"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.18", "sentence_id": 108, "sentence": "LeaveApplication.remarks displays as oj-text-area.", "color": "#9C27B0", "related_lines": [], "related_keywords": [{"keyword": "oj-text-area", "description": "Oracle JET multi-line text input component"}, {"keyword": "remarks", "description": "Field for approval/rejection comments"}, {"keyword": "multi-line", "description": "Allows multiple lines of text"}, {"keyword": "editable", "description": "HR manager can input comments"}, {"keyword": "UI component", "description": "Front-end element specification"}]}, {"id": "LO3.19", "sentence_id": 109, "sentence": "LeaveApplication.leaveID must exist. Error message: \"Invalid leave application reference\"", "color": "#E91E63", "related_lines": [1054, 1055, 1056], "related_keywords": [{"keyword": "getLeaveApplicationById", "description": "Method to find application"}, {"keyword": "if", "description": "Conditional check"}, {"keyword": "==", "description": "Equality comparison"}, {"keyword": "null", "description": "Check for non-existent record"}, {"keyword": "throw", "description": "Throw exception on validation failure"}, {"keyword": "ValidationException", "description": "Custom exception for validation errors"}, {"keyword": "exists", "description": "Must be found in database"}]}, {"id": "LO3.20", "sentence_id": 110, "sentence": "LeaveApplication.status must be one of \"Pending\", \"Approved\", \"Rejected\". Error message: \"Please select a valid approval status\"", "color": "#E91E63", "related_lines": [1062, 258, 259, 260, 261], "related_keywords": [{"keyword": "LeaveStatus", "description": "Enum type for status validation"}, {"keyword": "setStatus", "description": "Method to set status"}, {"keyword": "PENDING", "description": "Valid initial status"}, {"keyword": "APPROVED", "description": "Valid status option"}, {"keyword": "REJECTED", "description": "Valid status option"}, {"keyword": "enum", "description": "Enumeration validation"}, {"keyword": "valid options", "description": "Limited set of allowed values"}]}, {"id": "LO3.21", "sentence_id": 111, "sentence": "LeaveApplication.remarks must be provided when status is \"Rejected\". Error message: \"Please provide a reason for rejection\"", "color": "#E91E63", "related_lines": [164, 165, 166], "related_keywords": [{"keyword": "validate", "description": "Method containing validation logic"}, {"keyword": "if", "description": "Conditional check"}, {"keyword": "==", "description": "Equality comparison"}, {"keyword": "LeaveStatus.REJECTED", "description": "Status requiring remarks"}, {"keyword": "&&", "description": "AND condition"}, {"keyword": "==", "description": "Second equality check"}, {"keyword": "null", "description": "Check for missing remarks"}, {"keyword": "||", "description": "OR condition"}, {"keyword": "trim", "description": "Remove whitespace"}, {"keyword": "isEmpty", "description": "Check for empty string"}, {"keyword": "throw", "description": "Throw exception on validation failure"}, {"keyword": "ValidationException", "description": "Custom exception for validation errors"}]}, {"id": "LO3.22", "sentence_id": 112, "sentence": "Terminal - no further routing", "color": "#795548", "related_lines": [1068], "related_keywords": [{"keyword": "return", "description": "End of workflow method"}, {"keyword": "terminal", "description": "No additional processing required"}, {"keyword": "save", "description": "Final database operation"}, {"keyword": "workflow", "description": "Process ends here"}, {"keyword": "complete", "description": "No further steps needed"}]}]}, {"stack_name": "Role: Employee", "steps": [{"id": "R1", "sentence_id": 113, "sentence": "Can Read, Create LeaveApplication entities", "color": "#5C6BC0", "related_lines": [406, 505, 506, 507, 508, 1012, 1013], "related_keywords": [{"keyword": "Read", "description": "Permission to view leave applications"}, {"keyword": "Create", "description": "Permission to submit new leave applications"}, {"keyword": "LeaveApplication", "description": "Entity type for leave requests"}, {"keyword": "hasRole", "description": "Method to check if user has Employee role"}, {"keyword": "applyForLeave", "description": "Service method for creating leave applications"}, {"keyword": "public", "description": "Method accessible to Employee role"}, {"keyword": "Employee", "description": "Role name for regular employees"}, {"keyword": "permissions", "description": "Access rights granted to this role"}]}, {"id": "R1.1", "sentence_id": 114, "sentence": "Can Read User entities", "color": "#5C6BC0", "related_lines": [908, 914, 915, 916, 917, 918, 919, 920, 921, 922, 923], "related_keywords": [{"keyword": "Read", "description": "Permission to view user information"}, {"keyword": "User", "description": "Entity type for user data"}, {"keyword": "findByUsername", "description": "Method to retrieve user information"}, {"keyword": "getUserId", "description": "Access to user ID field"}, {"keyword": "getUsername", "description": "Access to username field"}, {"keyword": "getEmail", "description": "Access to email field"}, {"keyword": "public", "description": "Read access methods"}, {"keyword": "profile", "description": "User profile information"}]}, {"id": "R1.2", "sentence_id": 115, "sentence": "Can Read Role entities", "color": "#5C6BC0", "related_lines": [626, 627, 628, 692, 693, 700, 701, 505, 506, 507], "related_keywords": [{"keyword": "Read", "description": "Permission to view role information"}, {"keyword": "Role", "description": "Entity type for role data"}, {"keyword": "getRoleId", "description": "Access to role ID field"}, {"keyword": "getName", "description": "Access to role name field"}, {"keyword": "getDescription", "description": "Access to role description"}, {"keyword": "hasRole", "description": "Method to check assigned roles"}, {"keyword": "getRoles", "description": "Get list of assigned roles"}, {"keyword": "public", "description": "Read access methods"}]}, {"id": "R1.3", "sentence_id": 116, "sentence": "Can Execute ApplyForLeave function", "color": "#5C6BC0", "related_lines": [987, 1012, 1013, 1014, 1015, 1016, 1018, 1020, 1022, 1024], "related_keywords": [{"keyword": "Execute", "description": "Permission to run the function"}, {"keyword": "ApplyForLeave", "description": "Function name for submitting leave"}, {"keyword": "applyForLeave", "description": "Service method implementation"}, {"keyword": "public", "description": "Method accessible to Employee role"}, {"keyword": "LeaveApplication", "description": "Parameter and return type"}, {"keyword": "setLeaveId", "description": "Auto-generate leave ID"}, {"keyword": "generateLeaveId", "description": "Generate unique ID for leave"}, {"keyword": "setStatus", "description": "Set initial status to PENDING"}, {"keyword": "validate", "description": "Validate application data"}, {"keyword": "save", "description": "Persist to database"}]}]}, {"stack_name": "Role: Manager", "steps": [{"id": "R2", "sentence_id": 117, "sentence": "Can Read, Update LeaveApplication entities", "color": "#5C6BC0", "related_lines": [1027, 1028, 1029, 1036, 1037, 1038], "related_keywords": [{"keyword": "Read", "description": "Permission to view leave applications"}, {"keyword": "Update", "description": "Permission to modify leave application status"}, {"keyword": "LeaveApplication", "description": "Entity type for leave requests"}, {"keyword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Service method for manager approval"}, {"keyword": "getLeaveApplicationById", "description": "Read leave application by ID"}, {"keyword": "setStatus", "description": "Update application status"}, {"keyword": "setRemarks", "description": "Update approval/rejection remarks"}, {"keyword": "setApprovedBy", "description": "Set who approved the leave"}, {"keyword": "public", "description": "Methods accessible to Manager role"}, {"keyword": "3 days or less", "description": "Manager approval scope"}]}, {"id": "R2.1", "sentence_id": 118, "sentence": "Can Read User entities", "color": "#5C6BC0", "related_lines": [908, 914, 915, 916, 917, 918, 919, 920, 921, 922, 923, 930], "related_keywords": [{"keyword": "Read", "description": "Permission to view user information"}, {"keyword": "User", "description": "Entity type for user data"}, {"keyword": "findByUsername", "description": "Method to retrieve user information"}, {"keyword": "getUserId", "description": "Access to user ID field"}, {"keyword": "getUsername", "description": "Access to username field"}, {"keyword": "getEmail", "description": "Access to email field"}, {"keyword": "getFirstName", "description": "Access to first name"}, {"keyword": "getLastName", "description": "Access to last name"}, {"keyword": "getRoles", "description": "Access to user roles"}, {"keyword": "public", "description": "Read access methods"}, {"keyword": "employee details", "description": "Manager can view team member info"}]}, {"id": "R2.2", "sentence_id": 119, "sentence": "Can Read Role entities", "color": "#5C6BC0", "related_lines": [626, 627, 628, 692, 693, 700, 701, 505, 506, 507], "related_keywords": [{"keyword": "Read", "description": "Permission to view role information"}, {"keyword": "Role", "description": "Entity type for role data"}, {"keyword": "getRoleId", "description": "Access to role ID field"}, {"keyword": "getName", "description": "Access to role name field"}, {"keyword": "getDescription", "description": "Access to role description"}, {"keyword": "hasRole", "description": "Method to check role assignments"}, {"keyword": "getRoles", "description": "Get list of roles"}, {"keyword": "public", "description": "Read access methods"}, {"keyword": "hierarchy", "description": "Manager can view role hierarchy"}]}, {"id": "R2.3", "sentence_id": 120, "sentence": "Can Execute ManagerApproval function", "color": "#5C6BC0", "related_lines": [989, 990, 1027, 1028, 1029, 1030, 1031, 1040, 1041, 1042, 1044, 1046], "related_keywords": [{"keyword": "Execute", "description": "Permission to run the function"}, {"keyword": "ManagerApproval", "description": "Function name for manager approval"}, {"keyword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Service method implementation"}, {"keyword": "public", "description": "Method accessible to Manager role"}, {"keyword": "LeaveApplication", "description": "Parameter and return type"}, {"keyword": "LeaveStatus", "description": "Status parameter for decision"}, {"keyword": "String", "description": "Parameters for remarks and approver ID"}, {"keyword": "getNumDays", "description": "Check leave duration"}, {"keyword": "3", "description": "<PERSON><PERSON><PERSON><PERSON> for manager authority"}, {"keyword": "ValidationException", "description": "Error if exceeding authority"}, {"keyword": "validate", "description": "Validate approval data"}, {"keyword": "save", "description": "Persist approval decision"}]}]}, {"stack_name": "Role: HR Manager", "steps": [{"id": "R3", "sentence_id": 121, "sentence": "Can Read, Update LeaveApplication entities", "color": "#5C6BC0", "related_lines": [1049, 1050, 1051, 1058, 1059, 1060], "related_keywords": [{"keyword": "Read", "description": "Permission to view leave applications"}, {"keyword": "Update", "description": "Permission to modify leave application status"}, {"keyword": "LeaveApplication", "description": "Entity type for leave requests"}, {"keyword": "hrManagerApproval", "description": "Service method for HR manager approval"}, {"keyword": "getLeaveApplicationById", "description": "Read leave application by ID"}, {"keyword": "setStatus", "description": "Update application status"}, {"keyword": "setRemarks", "description": "Update approval/rejection remarks"}, {"keyword": "setApprovedBy", "description": "Set who approved the leave"}, {"keyword": "public", "description": "Methods accessible to HR Manager role"}, {"keyword": "more than 3 days", "description": "HR manager approval scope"}]}, {"id": "R3.1", "sentence_id": 122, "sentence": "Can Read, Update User entities", "color": "#5C6BC0", "related_lines": [908, 914, 915, 916, 917, 918, 919, 920, 521, 522, 523, 530, 531, 532, 554, 555, 556, 570, 571, 572, 578, 579, 580, 586, 587, 588, 594, 595, 596], "related_keywords": [{"keyword": "Read", "description": "Permission to view user information"}, {"keyword": "Update", "description": "Permission to modify user information"}, {"keyword": "User", "description": "Entity type for user data"}, {"keyword": "findByUsername", "description": "Method to retrieve user information"}, {"keyword": "setUsername", "description": "Update username field"}, {"keyword": "setEmail", "description": "Update email field"}, {"keyword": "setStatus", "description": "Update user status"}, {"keyword": "setDisabled", "description": "Enable/disable user account"}, {"keyword": "setOrganization", "description": "Update user organization"}, {"keyword": "setTeam", "description": "Update user team"}, {"keyword": "setRoles", "description": "Update user role assignments"}, {"keyword": "public", "description": "Read/write access methods"}, {"keyword": "HR administration", "description": "HR manager can manage user accounts"}]}, {"id": "R3.2", "sentence_id": 123, "sentence": "Can Read Role entities", "color": "#5C6BC0", "related_lines": [626, 627, 628, 692, 693, 700, 701, 505, 506, 507, 939, 940, 941, 942], "related_keywords": [{"keyword": "Read", "description": "Permission to view role information"}, {"keyword": "Role", "description": "Entity type for role data"}, {"keyword": "getRoleId", "description": "Access to role ID field"}, {"keyword": "getName", "description": "Access to role name field"}, {"keyword": "getDescription", "description": "Access to role description"}, {"keyword": "hasRole", "description": "Method to check role assignments"}, {"keyword": "loadUserRoles", "description": "Load all roles for a user"}, {"keyword": "public", "description": "Read access methods"}, {"keyword": "role management", "description": "HR manager can view and understand role structure"}]}, {"id": "R3.3", "sentence_id": 124, "sentence": "Can Execute HRApproval function", "color": "#5C6BC0", "related_lines": [992, 993, 1049, 1050, 1051, 1052, 1053, 1062, 1063, 1064, 1066, 1068], "related_keywords": [{"keyword": "Execute", "description": "Permission to run the function"}, {"keyword": "HRApproval", "description": "Function name for HR approval"}, {"keyword": "hrManagerApproval", "description": "Service method implementation"}, {"keyword": "public", "description": "Method accessible to HR Manager role"}, {"keyword": "LeaveApplication", "description": "Parameter and return type"}, {"keyword": "LeaveStatus", "description": "Status parameter for decision"}, {"keyword": "String", "description": "Parameters for remarks and approver ID"}, {"keyword": "getNumDays", "description": "Check leave duration"}, {"keyword": "3", "description": "Threshold for HR authority"}, {"keyword": "ValidationException", "description": "Error if not in HR scope"}, {"keyword": "validate", "description": "Validate approval data"}, {"keyword": "save", "description": "Persist approval decision"}, {"keyword": "leave balance", "description": "HR can verify employee leave balance"}]}]}, {"stack_name": "Dependent Dropdown: Leave Sub-Types", "steps": [{"id": "DD1", "sentence_id": 125, "sentence": "LeaveApplication.leaveSubType depends on LeaveApplication.leaveType", "color": "#5C6BC0", "related_lines": [879, 880, 885, 1077, 1078, 1079], "related_keywords": [{"keyword": "leaveSubType", "description": "Dependent field that changes based on selection"}, {"keyword": "leaveType", "description": "Parent field that determines available sub-types"}, {"keyword": "depends on", "description": "Relationship between parent and child fields"}, {"keyword": "findByLeaveTypeAndActiveTrue", "description": "DAO method to fetch filtered sub-types"}, {"keyword": "getLeaveSubTypesByLeaveType", "description": "Service method to get sub-types for a leave type"}, {"keyword": "WHERE", "description": "SQL clause for filtering"}, {"keyword": "AND active = true", "description": "Only show active sub-types"}, {"keyword": "dynamic", "description": "Options change based on parent selection"}]}, {"id": "DD1.1", "sentence_id": 126, "sentence": "When LeaveApplication.leaveType = \"Annual Leave\", LeaveApplication.leaveSubType options are \"Standard Annual Leave\", \"Carry-over Leave\", \"Compensatory Leave\"", "color": "#5C6BC0", "related_lines": [283, 284, 311, 879, 880, 885, 893, 894], "related_keywords": [{"keyword": "ANNUAL_LEAVE", "description": "Enum value for annual leave type"}, {"keyword": "LeaveType", "description": "Enum containing ANNUAL_LEAVE"}, {"keyword": "Standard Annual Leave", "description": "Sub-type option for regular vacation"}, {"keyword": "Carry-over Leave", "description": "Sub-type option for unused previous year leave"}, {"keyword": "Compensatory Leave", "description": "Sub-type option for overtime compensation"}, {"keyword": "findByLeaveTypeAndActiveTrue", "description": "Query that filters sub-types"}, {"keyword": "leave_type = ?", "description": "SQL parameter for filtering"}, {"keyword": "sub_type_name", "description": "Database field for sub-type name"}]}, {"id": "DD1.2", "sentence_id": 127, "sentence": "When LeaveApplication.leaveType = \"Sick Leave\", LeaveApplication.leaveSubType options are \"Short-term Illness\", \"Hospitalization\", \"Chronic Condition\"", "color": "#5C6BC0", "related_lines": [283, 285, 311, 879, 880, 885, 893, 894], "related_keywords": [{"keyword": "SICK_LEAVE", "description": "Enum value for sick leave type"}, {"keyword": "LeaveType", "description": "Enum containing SICK_LEAVE"}, {"keyword": "Short-term Illness", "description": "Sub-type option for minor illnesses"}, {"keyword": "Hospitalization", "description": "Sub-type option for hospital stays"}, {"keyword": "Chronic Condition", "description": "Sub-type option for ongoing health issues"}, {"keyword": "findByLeaveTypeAndActiveTrue", "description": "Query that filters sub-types"}, {"keyword": "medical certificate", "description": "Required for sick leave > 3 days"}, {"keyword": "sub_type_name", "description": "Database field for sub-type name"}]}, {"id": "DD1.3", "sentence_id": 128, "sentence": "When LeaveApplication.leaveType = \"Parental Leave\", LeaveApplication.leaveSubType options are \"Maternity Leave\", \"Paternity Leave\", \"Adoption Leave\"", "color": "#5C6BC0", "related_lines": [283, 286, 311, 879, 880, 885, 893, 894], "related_keywords": [{"keyword": "PARENTAL_LEAVE", "description": "Enum value for parental leave type"}, {"keyword": "LeaveType", "description": "Enum containing PARENTAL_LEAVE"}, {"keyword": "Maternity Leave", "description": "Sub-type option for mothers"}, {"keyword": "Paternity Leave", "description": "Sub-type option for fathers"}, {"keyword": "Adoption Leave", "description": "Sub-type option for adoptive parents"}, {"keyword": "findByLeaveTypeAndActiveTrue", "description": "Query that filters sub-types"}, {"keyword": "leave_type = ?", "description": "SQL parameter for filtering"}, {"keyword": "sub_type_name", "description": "Database field for sub-type name"}]}, {"id": "DD1.4", "sentence_id": 129, "sentence": "When LeaveApplication.leaveType = \"Bereavement\", LeaveApplication.leaveSubType options are \"Immediate Family\", \"Extended Family\"", "color": "#5C6BC0", "related_lines": [283, 287, 311, 879, 880, 885, 893, 894], "related_keywords": [{"keyword": "BEREAVEMENT", "description": "Enum value for bereavement leave type"}, {"keyword": "LeaveType", "description": "Enum containing BEREAVEMENT"}, {"keyword": "Immediate Family", "description": "Sub-type option for close relatives"}, {"keyword": "Extended Family", "description": "Sub-type option for distant relatives"}, {"keyword": "findByLeaveTypeAndActiveTrue", "description": "Query that filters sub-types"}, {"keyword": "leave_type = ?", "description": "SQL parameter for filtering"}, {"keyword": "sub_type_name", "description": "Database field for sub-type name"}, {"keyword": "active = true", "description": "Only show currently available options"}]}]}, {"stack_name": "Tenant Configuration", "steps": [{"id": "T1", "sentence_id": 130, "sentence": "Tenant: LeaveManagement001", "color": "#5C6BC0", "related_lines": [630, 640, 652, 716, 957, 1160], "related_keywords": [{"keyword": "tenant", "description": "Multi-tenant organizational identifier"}, {"keyword": "LeaveManagement001", "description": "Specific tenant ID for this leave management instance"}, {"keyword": "tenantId", "description": "Field in Role and other entities for tenant scope"}, {"keyword": "getTenantId", "description": "Method to retrieve tenant identifier"}, {"keyword": "setTenantId", "description": "Method to set tenant identifier"}, {"keyword": "FK", "description": "Foreign key reference to tenant"}, {"keyword": "VARCHAR", "description": "Database column type for tenant ID"}, {"keyword": "scope", "description": "Tenant defines data isolation boundary"}]}, {"id": "T1.1", "sentence_id": 131, "sentence": "Roles: Em<PERSON>loyee, Manager, HR Manager", "color": "#5C6BC0", "related_lines": [627, 693, 505, 506, 507, 1027, 1049, 1012], "related_keywords": [{"keyword": "Roles", "description": "User roles defined within this tenant"}, {"keyword": "Employee", "description": "Basic user role for submitting leave"}, {"keyword": "Manager", "description": "Role for approving short leaves"}, {"keyword": "HR Manager", "description": "Role for approving long leaves and user management"}, {"keyword": "hasRole", "description": "Method to check if user has specific role"}, {"keyword": "name", "description": "Role name field"}, {"keyword": "tenant specific", "description": "Roles are scoped to this tenant"}, {"keyword": "permissions", "description": "Each role has different access rights"}]}, {"id": "T1.2", "sentence_id": 132, "sentence": "Workflow: Leave Application Workflow", "color": "#5C6BC0", "related_lines": [986, 987, 989, 991, 1012, 1027, 1049, 258, 259, 260, 261], "related_keywords": [{"keyword": "Workflow", "description": "Process definition for leave applications"}, {"keyword": "Leave Application Workflow", "description": "Main workflow name for this tenant"}, {"keyword": "LeaveApplicationService", "description": "Service interface for workflow operations"}, {"keyword": "states", "description": "PENDING, APPROVED, REJECTED"}, {"keyword": "transitions", "description": "State change rules in workflow"}, {"keyword": "applyForLeave", "description": "Initial workflow step"}, {"keyword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Manager step in workflow"}, {"keyword": "hrManagerApproval", "description": "HR manager step in workflow"}]}, {"id": "T1.3", "sentence_id": 133, "sentence": "Local Objectives: ApplyForLeave, ManagerApproval, HRManagerApproval", "color": "#5C6BC0", "related_lines": [1012, 1013, 1027, 1028, 1049, 1050], "related_keywords": [{"keyword": "Local Objectives", "description": "Specific functions within the workflow"}, {"keyword": "ApplyForLeave", "description": "Employee function to submit leave"}, {"keyword": "ManagerApproval", "description": "Manager function to approve short leaves"}, {"keyword": "HRManagerApproval", "description": "HR function to approve long leaves"}, {"keyword": "@Override", "description": "Service method implementations"}, {"keyword": "public", "description": "Accessible methods"}, {"keyword": "LeaveApplication", "description": "Parameter and return type for objectives"}, {"keyword": "tenant specific", "description": "Objectives configured for this tenant"}]}, {"id": "T1.4", "sentence_id": 134, "sentence": "Entities: LeaveApplication, LeaveSubType, User, Role", "color": "#5C6BC0", "related_lines": [18, 309, 394, 624, 1115, 1131, 1140, 1155], "related_keywords": [{"keyword": "Entities", "description": "Data models defined for this tenant"}, {"keyword": "LeaveApplication", "description": "Main entity for leave requests"}, {"keyword": "LeaveSubType", "description": "Entity for leave categories"}, {"keyword": "User", "description": "Entity for system users"}, {"keyword": "Role", "description": "Entity for user permissions"}, {"keyword": "public class", "description": "Entity class definitions"}, {"keyword": "CREATE TABLE", "description": "Database table creation"}, {"keyword": "tenant scope", "description": "All entities belong to this tenant"}, {"keyword": "relationships", "description": "Entities are interconnected"}]}]}]}