import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_alert_dialog.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/auth_provider.dart';
import '../../ui_components/inputs/app_text_field.dart';
import '../../ui_components/theme/app_theme.dart';
import '../../utils/logger.dart';
import '../../utils/navigation_service.dart';
import '../../utils/validators.dart';
import '../../widgets/auth/auth_button.dart';
import '../../widgets/auth/auth_link.dart';
import '../../widgets/auth/password_field.dart';
import '../../widgets/auth/validated_text_field.dart';

import 'base_auth_screen.dart';
import 'register_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _emailController = TextEditingController(text: "Mentor1");
  final _passwordController = TextEditingController(text: "mentor123");

  final _formKey = GlobalKey<FormState>();
  bool _rememberMe = false;
  bool _isManualLoading = false; // Manual loading state for testing

  @override
  void initState() {
    super.initState();
    Logger.info('LoginScreen initialized');

    // Check if we need to pre-fill the form with saved credentials
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      Logger.info('LoginScreen post-frame callback');
      Logger.info(
          'AuthProvider state - Remember Me: ${authProvider.rememberMe}');
      Logger.info(
          'AuthProvider state - Saved Email: ${authProvider.savedEmail}');
      Logger.info(
          'AuthProvider state - Has Password: ${authProvider.savedPassword != null}');

      // Pre-fill form fields
      setState(() {
        _rememberMe = authProvider.rememberMe;
        Logger.info('Setting Remember Me checkbox to: $_rememberMe');

        // Pre-fill email and password if remember me is checked
        if (_rememberMe && authProvider.savedEmail != null) {
          _emailController.text = authProvider.savedEmail!;
          Logger.info('Pre-filled email: ${_emailController.text}');

          if (authProvider.savedPassword != null) {
            _passwordController.text = authProvider.savedPassword!;
            Logger.info('Pre-filled password: ******');
          }
        }
      });
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        // Set manual loading state to true
        setState(() {
          _isManualLoading = true;
        });

        final authProvider = Provider.of<AuthProvider>(context, listen: false);

        // Debug log for initial loading state
        Logger.info(
            'Initial loading state: ${authProvider.isLoading}, Manual loading: $_isManualLoading');

        // Set remember me preference
        Logger.info('Setting Remember Me preference to: $_rememberMe');
        await authProvider.setRememberMe(_rememberMe);

        // Attempt login
        final email = _emailController.text.trim();
        final password = _passwordController.text;
        Logger.info(
            'Attempting login for email: $email with Remember Me: $_rememberMe');

        // Debug log before login
        Logger.info(
            'Loading state before login: ${authProvider.isLoading}, Manual loading: $_isManualLoading');

        final success = await authProvider.login(email, password);
        // final success = true;

        // Debug log after login
        Logger.info(
            'Loading state after login: ${authProvider.isLoading}, Manual loading: $_isManualLoading');

        // Reset manual loading state
        if (mounted) {
          setState(() {
            _isManualLoading = false;
          });
        }
        // final success = true;

        // Check if login was successful
        if (success) {
          Logger.info('Login successful, navigating to chat screen');

          // Use NavigationService to navigate without context

          Future.microtask(() {
            NavigationService.navigateToHome();
            Logger.info('Navigation completed using NavigationService');
          });
          // Future.microtask(() {
          //   NavigationService.navigateToChat();
          //   Logger.info('Navigation completed using NavigationService');
          // });
        } else {
          Logger.info('Login failed');

          // Show error dialog
          if (authProvider.error != null) {
            _showErrorDialog(authProvider.error!);
          } else {
            _showErrorDialog(
                'Login failed. Please check your credentials and try again.');
          }
        }
      } catch (e) {
        Logger.error('Error during login process: $e');

        // Reset manual loading state in case of error
        if (mounted) {
          setState(() {
            _isManualLoading = false;
          });
        }

        // Show error dialog for exceptions
        _showErrorDialog('An error occurred during login. Please try again.');
      } finally {
        // Make sure loading state is reset
        if (mounted && _isManualLoading) {
          setState(() {
            _isManualLoading = false;
          });
        }
      }
    } else {
      Logger.info('Form validation failed');
    }
  }

  // Show error dialog
  void _showErrorDialog(String errorMessage) {
    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() {
      // if (!mounted) return;
  showDialog(
        context: context,
        builder: (BuildContext context) => CustomAlertDialog(
          title: 'common.error',
          content:
              errorMessage,
          onClose: () => Navigator.of(context).pop(),
          primaryButtonText: 'common.close',
          onPrimaryPressed: () {
            // Handle primary action
            Navigator.of(context).pop();
          },
          // secondaryButtonText: 'Cancel',
          // onSecondaryPressed: () => Navigator.of(context).pop(),
        ),
      );
   
      // showDialog(
      //   context: context,
      //   builder: (BuildContext context) {
      //     return AlertDialog(
      //       title: Row(
      //         children: [
      //           Icon(Icons.error_outline, color: AppTheme.errorColor),
      //           const SizedBox(width: 8),
      //           Text(context.tr('common.error')),
      //         ],
      //       ),
      //       content: Text(errorMessage),
      //       actions: [
      //         TextButton(
      //           onPressed: () {
      //             Navigator.of(context).pop();
      //           },
      //           child: Text(context.tr('common.close')),
      //         ),
      //       ],
      //     );
      //   },
      // );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        // Debug log for loading state in build method
        Logger.info(
            'Build method - AuthProvider loading: ${authProvider.isLoading}, Manual loading: $_isManualLoading');

        return SafeArea(
          child: BaseAuthScreen(
            title: context.tr('auth.welcomeBack'),
            subtitle: context.tr('auth.pleaseSignIn'),
            isLoading: authProvider.isLoading ||
                _isManualLoading, // Use either loading state
            errorMessage: authProvider.error,
            form: _buildLoginForm(context, authProvider),
          ),
        );
      },
    );
  }

  Widget _buildLoginForm(BuildContext context, AuthProvider authProvider) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Email Field
          ValidatedTextField(
            controller: _emailController,
            label: context.tr('auth.username'),
            placeholder: 'Enter your username',
            type: AppTextFieldType.email,
            textInputAction: TextInputAction.next,
            // prefix: const Icon(Icons.email_outlined),
            enabled: !(authProvider.isLoading || _isManualLoading),
            validator: Validators.validateUsername,
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Password Field
          PasswordField(
            controller: _passwordController,
            label: context.tr('auth.password'),
            placeholder: 'Enter your password',
            textInputAction: TextInputAction.done,
            enabled: !(authProvider.isLoading || _isManualLoading),
            validator: Validators.validatePassword,
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Remember Me and Forgot Password
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Remember Me Checkbox
              Row(
                children: [
                  Checkbox(
                    value: _rememberMe,
                    onChanged: (authProvider.isLoading || _isManualLoading)
                        ? null
                        : (value) {
                            setState(() {
                              _rememberMe = value ?? false;
                            });
                          },
                  ),
                  Text(
                    context.tr('auth.rememberMe'),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),

              // Forgot Password Button
              TextButton(
                onPressed: (authProvider.isLoading || _isManualLoading)
                    ? null
                    : () {
                        // TODO: Implement forgot password
                        Logger.info('Forgot password tapped');
                      },
                child: Text(
                  context.tr('auth.forgotPassword'),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingL),

          // Login Button
          AuthButton(
            text: context.tr('auth.login'),
            onPressed:
                (authProvider.isLoading || _isManualLoading) ? null : _login,
            isLoading: authProvider.isLoading || _isManualLoading,
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Register Button
          AuthLink(
            text: "${context.tr('auth.dontHaveAccount')} ",
            linkText: context.tr('auth.register'),
            onPressed: () {
              NavigationService.push(const RegisterScreen());
              Logger.info('Register button tapped');
            },
            isDisabled: authProvider.isLoading || _isManualLoading,
          ),
        ],
      ),
    );
  }
}
