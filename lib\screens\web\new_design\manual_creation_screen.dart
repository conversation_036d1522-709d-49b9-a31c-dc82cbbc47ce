import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import 'widgets/sidebar.dart';
import '../../../providers/manual_creation_provider.dart';
import '../../../models/role_info.dart';
import 'widgets/chat_widgets/build_role_card.dart';
import 'widgets/workflow_tree_widget.dart';

class ManualCreationScreen extends StatelessWidget {
  const ManualCreationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _ManualCreationScreenContent();
  }
}

class _ManualCreationScreenContent extends StatefulWidget {
  @override
  _ManualCreationScreenContentState createState() => _ManualCreationScreenContentState();
}

class _ManualCreationScreenContentState extends State<_ManualCreationScreenContent> {
  String? _lastShownError;
  String? _lastShownResult;
  String? _activeIconView; // Track which icon view is active

  @override
  Widget build(BuildContext context) {
    return Consumer<ManualCreationProvider>(
      builder: (context, provider, child) {
        // Show alert dialog for validation results
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showValidationAlertDialog(context, provider);
        });

        return _buildMainContent(context, provider);
      },
    );
  }

  Widget _buildMainContent(BuildContext context, ManualCreationProvider provider) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: Row(
            children: [

              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 5.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // SizedBox(height: 20), // Align with text field

                      // Agents icon
                      _buildIconWithTooltip(
                        provider: provider,
                        iconPath: 'assets/images/agent.svg',
                        tooltipText: 'Agents',
                        onTap: provider.handleAgentsTap,
                      ),

                      SizedBox(height: 10),

                      // DataSets icon
                      _buildIconWithTooltip(
                        provider: provider,
                        iconPath: 'assets/images/cube-box.svg',
                        tooltipText: 'DataSets',
                        onTap: provider.handleDataSetsTap,
                      ),

                      SizedBox(height: 10),

                      // Workflows icon
                      _buildIconWithTooltip(
                        provider: provider,
                        iconPath: 'assets/images/square-box-uncheck.svg',
                        tooltipText: 'Workflows',
                        onTap: provider.handleWorkflowsTap,
                      ),
                    ],
                  ),
                ),
              ),

              // Main content area
              Expanded(
                flex: 7,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: AppSpacing.sm),
                    // Header with back button
                    GestureDetector(
                       onTap: () {
              Provider.of<WebHomeProvider>(context, listen: false)
                  .currentScreenIndex = ScreenConstants.home;
            },
                      child: MouseRegion(
                         cursor: SystemMouseCursors.click,
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/images/arrow-left.svg',
                              height: 12,
                              width: 12,
                              color: Colors.black,
                            ),
                            // IconButton(
                            //   icon: Icon(Icons.arrow_back, color: Colors.black),
                            //   onPressed: () {
                            //     Navigator.of(context).pop();
                            //   },
                            //),
                            SizedBox(width: 8),
                            Text(
                              'Previous page',
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xff5D5D5D),
                                fontWeight: FontWeight.w400,
                                fontFamily: 'TiemposText',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(height: 24),

                    // Title
                    Text(
                      _getTitle(provider),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'TiemposText',
                        color: Colors.black,
                      ),
                    ),

                    SizedBox(height: 2),

                    // Main content area with icons and text field
                    Expanded(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [

                          // Left side - Icons column

                          // SizedBox(width: 16),

                          // Right side - Large text field or table
                          Expanded(
                            child: _buildWorkflowContent(context, provider),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 8),

                    // Bottom buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Cancel button
                        TextButton(
                          onPressed: () {
                           // Navigator.of(context).pop();
                          },
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                                horizontal: 30, vertical: 10),
                            side:
                                BorderSide(color: Color(0xffD0D0D0), width: 1),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              fontFamily: 'TiemposText',
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),

                        SizedBox(width: 20),

                        // Previous button (show in entity and workflow creation steps)
                        if (provider.currentStep == WorkflowStep.entityCreation ||
                            provider.currentStep == WorkflowStep.workflowCreation)
                          ElevatedButton(
                            onPressed: provider.goToPreviousStep,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey.shade600,
                              padding: EdgeInsets.symmetric(
                                  horizontal: 30, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            child: Text(
                              'Previous',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontFamily: 'TiemposText',
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),

                        if (provider.currentStep == WorkflowStep.entityCreation ||
                            provider.currentStep == WorkflowStep.workflowCreation)
                          SizedBox(width: 20),

                        // Main action button
                        ElevatedButton(
                          onPressed: _isValidating(provider) ? null : () => _handleButtonPress(provider),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _isValidating(provider)
                                ? Colors.grey.shade400
                                : Color(0xff0058FF),
                            padding: EdgeInsets.symmetric(
                                horizontal: 30, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          child: _isValidating(provider)
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                      ),
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Validating...',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontFamily: 'TiemposText',
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                )
                              : Text(
                                  _getButtonText(provider),
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                        ),

                      ],
                    ),
                   SizedBox(height: 8),
                  ],
                ),
              ),
              Expanded(child: SizedBox()),
            ],
          ),
        );
  }

  // Show validation results in alert dialog
  void _showValidationAlertDialog(BuildContext context, ManualCreationProvider provider) {
    // Handle agent validation errors
    if (provider.validationError != null && _lastShownError != provider.validationError) {
      _lastShownError = provider.validationError;
      _showErrorAlertDialog(context, 'Agent Validation Error', provider.validationError!);
    } else if (provider.validationResult != null &&
               provider.validationResult!.validationErrors != null &&
               provider.validationResult!.validationErrors!.isNotEmpty) {
      // Show agent validation errors in alert dialog
      final errors = provider.validationResult!.validationErrors!;
      final errorMessages = errors.map((e) => '• ${e.message ?? 'Validation error'}').toList();

      if (_lastShownResult != errorMessages.join('\n')) {
        _lastShownResult = errorMessages.join('\n');
        _showValidationErrorsAlertDialog(context, 'Agent Validation Errors', errorMessages);
      }
    } else if (provider.validationResult != null &&
               provider.showAgentTable &&
               _lastShownResult != 'agent_success') {
      // Show success message when agent table is shown
      _lastShownResult = 'agent_success';
      _showSuccessAlertDialog(context, 'Agent Validation Successful', 'Agent data loaded successfully.');
    }

    // Handle entity validation errors
    else if (provider.entityValidationError != null && _lastShownError != provider.entityValidationError) {
      _lastShownError = provider.entityValidationError;
      _showErrorAlertDialog(context, 'Entity Validation Error', provider.entityValidationError!);
    } else if (provider.entityValidationResult != null &&
               provider.showEntityTable &&
               _lastShownResult != 'entity_success') {
      // Show success message when entity table is shown
      _lastShownResult = 'entity_success';
      _showSuccessAlertDialog(context, 'Entity Validation Successful', 'Entity data loaded successfully.');
    }

    // Handle workflow validation errors
    else if (provider.workflowValidationError != null && _lastShownError != provider.workflowValidationError) {
      _lastShownError = provider.workflowValidationError;
      _showErrorAlertDialog(context, 'Workflow Validation Error', provider.workflowValidationError!);
    } else if (provider.workflowValidationResult != null &&
               provider.showWorkflowTable &&
               _lastShownResult != 'workflow_success') {
      // Show success message when workflow table is shown
      _lastShownResult = 'workflow_success';
      _showSuccessAlertDialog(context, 'Workflow Validation Successful', 'Workflow data loaded successfully.');
    }
  }

  // Show error alert dialog
  void _showErrorAlertDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
               maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show validation errors alert dialog
  void _showValidationErrorsAlertDialog(BuildContext context, String title, List<String> messages) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.warning_outlined, color: Colors.orange, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.orange,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Container(
                  width: double.maxFinite,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: messages.map((message) => Padding(
                      padding: EdgeInsets.only(bottom: 8),
                      child: Text(
                        message,
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'TiemposText',
                          color: Colors.black87,
                        ),
                      ),
                    )).toList(),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show success alert dialog
  void _showSuccessAlertDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.check_circle_outline, color: Colors.green, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Helper methods for workflow
  String _getTitle(ManualCreationProvider provider) {
    // If an icon view is active, show that title
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          return provider.showAgentTable ? 'Agents' : 'Create Your Agents';
        case 'DataSets':
          return provider.showEntityTable ? 'Entity List' : 'Create Your Entity';
        case 'Workflows':
          return provider.showWorkflowTable ? 'Workflow List' : 'Create Your Workflow';
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable ? 'Agents' : 'Create Your Agents';
      case WorkflowStep.entityCreation:
        return provider.showEntityTable ? 'Entity List' : 'Create Your Entity';
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable ? 'Workflow List' : 'Create Your Workflow';
    }
  }

  String _getButtonText(ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          return provider.showAgentTable ? 'Done' : 'Validate';
        case 'DataSets':
          return provider.showEntityTable ? 'Done' : 'Validate';
        case 'Workflows':
          return provider.showWorkflowTable ? 'Done' : 'Validate';
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable ? 'Next' : 'Validate';
      case WorkflowStep.entityCreation:
        return provider.showEntityTable ? 'Next' : 'Validate';
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable ? 'Finish' : 'Validate';
    }
  }

  bool _isValidating(ManualCreationProvider provider) {
    return provider.isValidating || provider.isValidatingEntity || provider.isValidatingWorkflow;
  }

  void _handleButtonPress(ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          if (provider.showAgentTable) {
            // Handle next action for agents (could navigate or finish)
            _showSuccessAlertDialog(context, 'Agents Complete', 'Agent data processed successfully.');
          } else {
            // Call agent validation API
            provider.handleValidate();
          }
          break;
        case 'DataSets':
          if (provider.showEntityTable) {
            // Handle next action for entities (could navigate or finish)
            _showSuccessAlertDialog(context, 'Entities Complete', 'Entity data processed successfully.');
          } else {
            // Call entity validation API
            provider.handleValidate();
          }
          break;
        case 'Workflows':
          if (provider.showWorkflowTable) {
            // Handle next action for workflows (could navigate or finish)
            _showSuccessAlertDialog(context, 'Workflows Complete', 'Workflow data processed successfully.');
          } else {
            // Call workflow validation API
            provider.handleValidate();
          }
          break;
      }
      return;
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        if (provider.showAgentTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.entityCreation:
        if (provider.showEntityTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.workflowCreation:
        if (provider.showWorkflowTable) {
          // Handle finish action (navigate away or complete workflow)
          _handleFinishWorkflow(provider);
        } else {
          provider.handleValidate();
        }
        break;
    }
  }

  void _handleFinishWorkflow(ManualCreationProvider provider) {
    // Show success message and navigate back or reset
    _showSuccessAlertDialog(context, 'Workflow Complete', 'Agent, entity, and workflow creation completed successfully.');
    // Optionally reset the workflow or navigate away
    // provider.resetWorkflow();
  }

  Widget _buildWorkflowContent(BuildContext context, ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          // Show text field first, then table after validation
          return provider.showAgentTable
              ? _buildAgentTable(provider)
              : _buildTextField(context, provider);
        case 'DataSets':
          // Show text field first, then table after validation
          return provider.showEntityTable
              ? _buildEntityTable(provider)
              : _buildTextField(context, provider);
        case 'Workflows':
          // Show text field first, then table after validation
          return provider.showWorkflowTable
              ? _buildWorkflowTable(provider)
              : _buildTextField(context, provider);
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable
            ? _buildAgentTable(provider)
            : _buildTextField(context, provider);
      case WorkflowStep.entityCreation:
        return provider.showEntityTable
            ? _buildEntityTable(provider)
            : _buildTextField(context, provider);
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable
            ? _buildWorkflowTable(provider)
            : _buildTextField(context, provider);
    }
  }

  Widget _buildTextField(BuildContext context, ManualCreationProvider provider) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400, width: 1),
        borderRadius: BorderRadius.circular(2),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          focusColor: Colors.transparent,
        ),
        child: TextField(
          controller: provider.textController,
          maxLines: null,
          expands: true,
          textAlignVertical: TextAlignVertical.top,
          decoration: InputDecoration(
            hintStyle: TextStyle(
              color: Colors.grey.shade500,
              fontSize: 16,
              fontFamily: 'TiemposText',
            ),
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            contentPadding: EdgeInsets.all(16),
          ),
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'TiemposText',
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildEntityTable(ManualCreationProvider provider) {
    if (provider.extractedEntityData == null ||
        provider.extractedEntityData!.entityGroups == null ||
        provider.extractedEntityData!.entityGroups!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade400, width: 1),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Center(
          child: Text(
            'No entity data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400, width: 1),
        borderRadius: BorderRadius.circular(2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Entities and Relationships',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${provider.extractedEntityData!.entityGroups!.length} Entity Group${provider.extractedEntityData!.entityGroups!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.green.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Entity rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: provider.extractedEntityData!.entityGroups!.length,
              itemBuilder: (context, index) {
                final entityGroup = provider.extractedEntityData!.entityGroups![index];

                return Container(
                  margin: EdgeInsets.only(bottom: 12),
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300, width: 1),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Entity group title
                      Text(
                        entityGroup.title ?? 'Unknown Entity',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 8),

                      // Entity count
                      if (entityGroup.entities != null && entityGroup.entities!.isNotEmpty)
                        Text(
                          '${entityGroup.entities!.length} entit${entityGroup.entities!.length != 1 ? 'ies' : 'y'} in this group',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'TiemposText',
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkflowTable(ManualCreationProvider provider) {
    print('🌳 Building workflow table...');
    print('🌳 Show workflow table: ${provider.showWorkflowTable}');
    print('🌳 Extracted workflow data: ${provider.extractedWorkflowData?.length ?? 0} items');
    print('🌳 Current step: ${provider.currentStep}');
    print('🌳 Is validating workflow: ${provider.isValidatingWorkflow}');
    
    // Get tree nodes from provider
    print('🌳 About to call getWorkflowTreeNodes()...');
    final treeNodes = provider.getWorkflowTreeNodes();
    print('🌳 Tree nodes count: ${treeNodes.length}');
    
    if (treeNodes.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade400, width: 1),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Center(
          child: Text(
            'No workflow data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    print('🌳 Rendering tree widget with ${treeNodes.length} nodes');
    
    // Use the tree widget to display workflow data
    return WorkflowTreeWidget(
      nodes: treeNodes,
      onNodeTap: (node) {
        // Handle node tap - could show details or expand/collapse
        print('Node tapped: ${node.text}');
      },
      onNodeExpand: (node) {
        // Handle node expand/collapse
        print('Node expanded: ${node.text}');
      },
    );
  }
  // Build agent table similar to web_home_screen_chat.dart
  Widget _buildAgentTable(ManualCreationProvider provider) {
    if (provider.extractedAgentData == null ||
        provider.extractedAgentData!.agents == null ||
        provider.extractedAgentData!.agents!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade400, width: 1),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Center(
          child: Text(
            'No agent data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400, width: 1),
        borderRadius: BorderRadius.circular(2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Roles and Use Cases',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${provider.extractedAgentData!.agents!.length} Agent${provider.extractedAgentData!.agents!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Agent rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: provider.extractedAgentData!.agents!.length,
              itemBuilder: (context, index) {
                final agent = provider.extractedAgentData!.agents![index];

                // Convert AgentInfo to RoleInfo for BuildRoleCard
                final role = RoleInfo(
                  id: agent.id,
                  title: agent.title,
                  description: agent.description,
                  version: agent.version,
                  createdBy: agent.createdBy,
                  createdDate: _formatDate(agent.createdDate ?? DateTime.now()),
                  modifiedBy: agent.modifiedBy,
                  modifiedDate: _formatDate(agent.modifiedDate ?? DateTime.now()),
                  // Extract use cases from agent sections
                  useCases: agent.sections
                      .where((section) => section.title.toLowerCase().contains('responsibility'))
                      .expand((section) => section.items)
                      .toList(),
                  // Extract permissions from agent sections
                  permissions: {
                    'entities': agent.sections
                        .where((section) => section.title.toLowerCase().contains('authority'))
                        .expand((section) => section.items)
                        .toList(),
                    'objectives': agent.sections
                        .where((section) => section.title.toLowerCase().contains('kpi'))
                        .expand((section) => section.items)
                        .toList(),
                  },
                );

                return Padding(
                  padding: EdgeInsets.only(bottom: 8),
                  child: BuildRoleCard(
                    role: role,
                    isSelected: false,
                    onRoleTap: (selectedRole) {
                      // Handle role tap if needed
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Widget _buildIconWithTooltip({
    required ManualCreationProvider provider,
    required String iconPath,
    required String tooltipText,
    required VoidCallback onTap,
  }) {
    return _IconWithTooltip(
      iconPath: iconPath,
      tooltipText: tooltipText,
      isSelected: _activeIconView == tooltipText, // Check if this icon is selected
      onTap: () {
        // Set the active icon view
        setState(() {
          _activeIconView = tooltipText;
        });
        // Call the original onTap if needed
        onTap();
      },
    );
  }
}

class _IconWithTooltip extends StatefulWidget {
  final String iconPath;
  final String tooltipText;
  final VoidCallback onTap;
  final bool isSelected;

  const _IconWithTooltip({
    required this.iconPath,
    required this.tooltipText,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  State<_IconWithTooltip> createState() => _IconWithTooltipState();
}

class _IconWithTooltipState extends State<_IconWithTooltip> {
  bool isHovered = false;
  final GlobalKey _iconKey = GlobalKey();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _removeTooltip();
    super.dispose();
  }

  void _showTooltip() {
    try {
      _removeTooltip();

      // Get the position of the icon
      final RenderBox? renderBox =
          _iconKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final position = renderBox.localToGlobal(Offset.zero);
      final size = renderBox.size;

      _overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          left: position.dx + size.width + 8, // 8px gap from icon
          top: position.dy + (size.height / 2) - 18, // Center vertically
          child: Material(
            color: Colors.transparent,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(2),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xxs,
              ),
              child: Text(
                widget.tooltipText,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ),
        ),
      );

      // Check if the context is still valid before inserting the overlay
      if (!mounted) return;

      Overlay.of(context).insert(_overlayEntry!);
    } catch (e) {
      // Safely handle any errors that might occur when showing the tooltip
      print('Error showing tooltip: $e');
      _overlayEntry = null;
    }
  }

  void _removeTooltip() {
    try {
      if (_overlayEntry != null) {
        _overlayEntry!.remove();
        _overlayEntry = null;
      }
    } catch (e) {
      // Safely handle any errors that might occur when removing the overlay
      print('Error removing tooltip: $e');
      _overlayEntry = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine the icon color based on selected and hover states
    Color iconColor;
    if (widget.isSelected) {
      iconColor = Color(0xff0058FF); // Blue when selected
    } else if (isHovered) {
      iconColor = Color(0xff0058FF); // Blue when hovered
    } else {
      iconColor = Color(0xff797676); // Default gray
    }

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) {
        setState(() => isHovered = true);
        _showTooltip();
      },
      onExit: (_) {
        setState(() => isHovered = false);
        _removeTooltip();
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          key: _iconKey,
          padding: EdgeInsets.all(4), // Add padding for background
          decoration: BoxDecoration(
            color: widget.isSelected 
                ? Color(0xff0058FF).withOpacity(0.1) // Light blue background when selected
                : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: SvgPicture.asset(
            widget.iconPath,
            width: 11,
            height: 11,
            color: iconColor,
            // Add stroke width for bold effect when selected
            colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
          ),
        ),
      ),
    );
  }
}
