import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_alert_dialog.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../theme/spacing.dart';
import '../widgets/navigation_drawer.dart';
import '../widgets/chat_text_field.dart';
import '../widgets/nsl_logo.dart';
import '../utils/greeting_helper.dart';
import '../utils/input_value_store.dart';
import '../utils/logger.dart';
import '../widgets/responsive_builder.dart';
import '../providers/transaction_provider.dart';
import '../models/transaction.dart';
import '../models/global_objective.dart';
import '../models/transaction_status.dart';
import 'my_transactions_screen.dart';
import 'workflow_detail_screen_fixed.dart';
import 'existing_transactions_screen.dart';

class TransactionScreen extends StatefulWidget {
  const TransactionScreen({super.key});

  @override
  State<TransactionScreen> createState() => _TransactionScreenState();
}

class _TransactionScreenState extends State<TransactionScreen>
    with WidgetsBindingObserver {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Get the global input value store
  final _inputStore = InputValueStore();

  @override
  void initState() {
    super.initState();
    // Register this object as an observer
    WidgetsBinding.instance.addObserver(this);
    // Fetch data when the screen is first built
    _refreshDataOnNavigate();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // This will be called when the widget is inserted into the tree
    // and when the dependencies change (including when returning to this screen)
    _refreshDataOnNavigate();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Refresh data when the app comes back to the foreground
    if (state == AppLifecycleState.resumed) {
      _refreshDataOnNavigate();
    }
  }

  // Method to refresh data when navigating to this screen
  void _refreshDataOnNavigate() {
    // Use a short delay to ensure the widget is fully built
    Future.delayed(Duration.zero, () {
      if (mounted) {
        final transactionProvider =
            Provider.of<TransactionProvider>(context, listen: false);
        transactionProvider.fetchGlobalObjectives();
      }
    });
  }

  // Helper method to determine device type based on screen width
  DeviceType getDeviceType(double width) {
    if (width < 600) {
      return DeviceType.mobile;
    } else if (width < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  // Helper method to determine grid cross axis count - always return 2 as requested
  int _getGridCrossAxisCount(BuildContext context) {
    return 2; // Always show 2 items per row as requested
  }

  // Helper method to get card color based on status
  Color _getCardColorForStatus(String status) {
    switch (status.toLowerCase()) {
      case 'deployed':
        return Color.fromRGBO(33, 150, 243, 25); // Light blue
      case 'active':
        return Color.fromRGBO(156, 39, 176, 25); // Light purple
      case 'completed':
        return Color.fromRGBO(76, 175, 80, 25); // Light green
      case 'pending':
        return Color.fromRGBO(255, 152, 0, 25); // Light orange
      case 'failed':
        return Color.fromRGBO(244, 67, 54, 25); // Light red
      case 'inactive':
        return Color.fromRGBO(158, 158, 158, 25); // Light grey
      default:
        return Color.fromRGBO(0, 150, 136, 25); // Light teal
    }
  }

  // Helper method to build an objective card
  Widget _buildObjectiveCard(BuildContext context, GlobalObjective objective) {
    final cardColor = _getCardColorForStatus(objective.status);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: cardColor,
      child: InkWell(
        onTap: () {
          // Call a separate method to handle the tap to avoid async gap issues
          _handleObjectiveTap(context, objective);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status at top right
              Align(
                alignment: Alignment.topRight,
                child: Text(
                  objective.status.toUpperCase(),
                  style: TextStyle(
                    color: _getCardColorForStatus(objective.status),
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(height: 4),
              // Title takes four lines
              Text(
                objective.name,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                overflow: TextOverflow.ellipsis,
                maxLines: 4,
              ),
              const Spacer(),
              // ID and version in a row at the bottom
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //   children: [
              //     Text(
              //       'ID: ${objective.objectiveId}',
              //       style: Theme.of(context).textTheme.bodyMedium,
              //     ),
              //     Text(
              //       'v${objective.version}',
              //       style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              //             fontWeight: FontWeight.bold,
              //           ),
              //     ),
              //   ],
              // ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Remove the observer when the widget is disposed
    WidgetsBinding.instance.removeObserver(this);
    _textController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Helper method to handle objective card tap
  Future<void> _handleObjectiveTap(
      BuildContext context, GlobalObjective objective) async {
    // Show loading indicator
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(context.tr('transaction.checkingExistingTransactions')),
        duration: Duration(seconds: 1),
      ),
    );

    // Check if the GO is available in the user's transactions
    final transactionProvider =
        Provider.of<TransactionProvider>(context, listen: false);
    final transactions = await transactionProvider
        .validateGoAvailableInMyTransaction(objective.objectiveId);

    // Hide loading indicator
    scaffoldMessenger.hideCurrentSnackBar();

    // Check if the widget is still mounted before using context
    if (!mounted) return;

    // Store the context in a local variable to avoid async gap issues
    final currentContext = context;

    // Log the transactions data for debugging
    Logger.info('Transactions data: $transactions');

    // If there are existing transactions, show a popup dialog
    if (transactions.isNotEmpty) {
      // Show dialog asking if user wants to see existing transactions
      showDialog(
        context: context,
        builder: (dialogContext) => CustomAlertDialog(
          title: 'transaction.existingTransactions',
          content: 'transaction.existingTransactionsQuestion',
          onClose: () => Navigator.of(context).pop(),
          primaryButtonText: 'transaction.no',
          onPrimaryPressed: () {
            // Handle primary action
            Navigator.of(context).pop();
          },
          secondaryButtonText: 'transaction.yes',
          onSecondaryPressed: () {
            // Close the dialog
            Navigator.of(dialogContext).pop();

            // Navigate to existing transactions screen
            Navigator.push(
              currentContext,
              MaterialPageRoute(
                builder: (context) => ExistingTransactionsScreen(
                  objective: objective,
                  transactions: transactions,
                ),
              ),
            );
          },
        ),
      );

      //   showDialog(
      //     context: currentContext,
      //     builder: (dialogContext) => AlertDialog(
      //       title: Text(context.tr('transaction.existingTransactions')),
      //       content: Text(context.tr('transaction.existingTransactionsQuestion')),
      //       actions: [
      //         TextButton(
      //           onPressed: () {
      //             // Close the dialog
      //             Navigator.of(dialogContext).pop();

      //             // // Clear the input store before navigating to workflow detail screen
      //             // _inputStore.clear();

      //             // // Navigate to workflow detail screen to start a fresh transaction
      //             // Navigator.push(
      //             //   currentContext,
      //             //   MaterialPageRoute(
      //             //     builder: (context) =>
      //             //         WorkflowDetailScreen(objective: objective),
      //             //   ),
      //             // );
      //           },
      //           child: Text(context.tr('transaction.no')),
      //         ),
      //         ElevatedButton(
      //           onPressed: () {
      //             // Close the dialog
      //             Navigator.of(dialogContext).pop();

      //             // Navigate to existing transactions screen
      //             Navigator.push(
      //               currentContext,
      //               MaterialPageRoute(
      //                 builder: (context) => ExistingTransactionsScreen(
      //                   objective: objective,
      //                   transactions: transactions,
      //                 ),
      //               ),
      //             );
      //           },
      //           child: Text(context.tr('transaction.yes')),
      //         ),
      //       ],
      //     ),
      //   );
    } else {
      // Clear the input store before navigating to workflow detail screen
      _inputStore.clear();

      // Navigate to workflow detail screen to start a fresh transaction
      Navigator.push(
        currentContext,
        MaterialPageRoute(
          builder: (context) => WorkflowDetailScreen(objective: objective),
        ),
      );
    }
  }

  // Helper method to build status chip with different colors for different status
  Widget _buildStatusChip(TransactionStatus status) {
    Color chipColor;
    String statusText;

    switch (status) {
      case TransactionStatus.completed:
        chipColor = Colors.green;
        statusText = context.tr('transaction.completed');
        break;
      case TransactionStatus.pending:
        chipColor = Colors.orange;
        statusText = context.tr('transaction.pending');
        break;
      case TransactionStatus.failed:
        chipColor = Colors.red;
        statusText = context.tr('transaction.failed');
        break;
    }

    // Return just the text without any container decoration
    return Text(
      statusText,
      style: TextStyle(
        color: chipColor,
        fontWeight: FontWeight.bold,
        fontSize: 12,
      ),
    );
  }

  // Show transaction details in a modal bottom sheet
  void _showTransactionDetails(BuildContext context, Transaction transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.3,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 40,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      transaction.name,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    _buildStatusChip(transaction.status),
                  ],
                ),
                SizedBox(height: 10),
                Text(
                  transaction.description,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                SizedBox(height: 20),
                Text(
                  '${context.tr('transaction.id')}: ${transaction.id}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                SizedBox(height: 5),
                Text(
                  '${context.tr('transaction.timestamp')}: ${transaction.timestamp.toString()}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                SizedBox(height: 20),
                Text(
                  '${context.tr('transaction.data')}:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                SizedBox(height: 10),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      transaction.data.toString(),
                      style: TextStyle(fontFamily: 'monospace'),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        drawer: const AppNavigationDrawer(currentRoute: 'transact'),
        appBar: AppBar(
          title: Text(context.tr('transaction.transact')),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
        ),
        body: Column(
          children: [
            Expanded(
              child: Consumer<TransactionProvider>(
                builder: (context, transactionProvider, _) {
                  // Define the refresh function
                  Future<void> refreshData() async {
                    // Refresh both global objectives and transactions
                    await transactionProvider.fetchGlobalObjectives();
                    await transactionProvider.fetchTransactions();
                  }

                  // Show loading indicator when fetching initial data
                  if (transactionProvider.isLoading &&
                      transactionProvider.transactions.isEmpty &&
                      transactionProvider.isLoadingObjectives) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  // Wrap the content with RefreshIndicator
                  return RefreshIndicator(
                    onRefresh: refreshData,
                    child: Builder(builder: (context) {
                      // Show loading indicator when fetching objectives
                      if (transactionProvider.isLoadingObjectives) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      // Show global objectives if available
                      if (transactionProvider.globalObjectives.isNotEmpty) {
                        return Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Row with My Transactions button
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              const MyTransactionsScreen(),
                                        ),
                                      );
                                    },
                                    icon: Icon(
                                      Icons.history,
                                      // Use white icon for light theme, black for dark theme
                                      color: Theme.of(context).brightness ==
                                              Brightness.light
                                          ? Colors.white
                                          : Colors.black,
                                    ),
                                    label: Text(context
                                        .tr('transaction.myTransactions')),
                                    style: Theme.of(context)
                                        .elevatedButtonTheme
                                        .style,
                                  ),
                                ],
                              ),
                              SizedBox(height: AppSpacing.xl),
                              // Grid of objective cards
                              Expanded(
                                child: GridView.builder(
                                  physics:
                                      const AlwaysScrollableScrollPhysics(), // Important for RefreshIndicator to work
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount:
                                        _getGridCrossAxisCount(context),
                                    crossAxisSpacing: 16,
                                    mainAxisSpacing: 16,
                                    childAspectRatio:
                                        1.0, // Adjusted for four-line title
                                  ),
                                  itemCount: transactionProvider
                                      .globalObjectives.length,
                                  itemBuilder: (context, index) {
                                    final objective = transactionProvider
                                        .globalObjectives[index];
                                    return _buildObjectiveCard(
                                        context, objective);
                                  },
                                ),
                              ),
                            ],
                          ),
                        );
                      }

                      // Check if we have transactions
                      if (transactionProvider.transactions.isNotEmpty) {
                        return ListView.builder(
                          physics:
                              const AlwaysScrollableScrollPhysics(), // Important for RefreshIndicator
                          padding: const EdgeInsets.all(16.0),
                          itemCount: transactionProvider.transactions.length,
                          itemBuilder: (context, index) {
                            final transaction =
                                transactionProvider.transactions[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 16.0),
                              child: ListTile(
                                title: Text(transaction.name),
                                subtitle: Text(transaction.description),
                                trailing: _buildStatusChip(transaction.status),
                                onTap: () {
                                  // Show transaction details
                                  _showTransactionDetails(context, transaction);
                                },
                              ),
                            );
                          },
                        );
                      }

                      // Fallback to greeting if no objectives and no transactions
                      return ListView(
                        physics:
                            const AlwaysScrollableScrollPhysics(), // Important for RefreshIndicator
                        children: [
                          Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 32.0, vertical: 100.0),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // NSL Logo
                                  NSLLogo(
                                    size: 100.0,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                  const SizedBox(height: 20),
                                  Text(
                                    "How can I help you ${GreetingHelper.getTimeBasedGreeting()}?",
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineMedium
                                        ?.copyWith(
                                          fontWeight: FontWeight.w500,
                                          color: Theme.of(context)
                                              .textTheme
                                              .bodyLarge
                                              ?.color,
                                          fontSize: 28.0,
                                          height: 1.3,
                                          fontFamily: "TiemposText",
                                        ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 20),
                                  Text(
                                    'Enter transaction details below to execute NSL solutions',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      );
                    }),
                  );
                },
              ),
            ),
            // Chat text field at the bottom
            Consumer<TransactionProvider>(
              builder: (context, transactionProvider, _) {
                return ChatTextField(
                  controller: _textController,
                  hintText: 'Enter transaction details...',
                  isLoading: transactionProvider.isLoading,
                  onSubmitted: (value) {
                    // Execute the transaction
                    // Store the scaffold messenger before the async gap
                  },
                  onCancel: () {},
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
