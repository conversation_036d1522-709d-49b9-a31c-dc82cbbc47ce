import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_alert_dialog.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/chat_provider.dart';
import '../../models/message.dart';
import '../../models/conversation.dart';
import '../../widgets/chat_text_field.dart';
import '../../widgets/full_width_user_bubble.dart';
import '../../widgets/chat_nsl_bubble.dart';
import '../../utils/greeting_helper.dart';
import '../../services/speech_service.dart';
import '../../utils/logger.dart';

class WebChatScreen extends StatefulWidget {
  const WebChatScreen({super.key});

  @override
  State<WebChatScreen> createState() => _WebChatScreenState();
}

class _WebChatScreenState extends State<WebChatScreen> {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _focusNode = FocusNode();
  final SpeechService _speechService = SpeechService();

  @override
  void initState() {
    super.initState();
    _initializeSpeechService();
  }

  Future<void> _initializeSpeechService() async {
    await _speechService.initialize();
    Logger.info('Speech service initialized in WebChatScreen');
  }

  Future<void> _speakMessage(String message) async {
    // Check microphone permission first (needed for some devices)
    bool hasPermission = await _speechService.checkMicrophonePermission();
    if (!hasPermission) {
      // Request permission
      hasPermission = await _speechService.requestMicrophonePermission();
      if (!hasPermission) {
        // Show error message if permission denied
        _showPermissionDeniedDialog();
        return;
      }
    }

    Logger.info(
        'Speaking message: ${message.substring(0, message.length > 50 ? 50 : message.length)}...');
    await _speechService.speak(message);
  }

  void _showPermissionDeniedDialog() {
    showDialog(
        context: context,
        builder: (BuildContext context) => CustomAlertDialog(
          title: 'chat.microphonePermissionRequired',
          content:
              'chat.microphonePermissionMessage',
          onClose: () => Navigator.of(context).pop(),
          primaryButtonText: 'common.ok',
          onPrimaryPressed: () {
            // Handle primary action
            Navigator.of(context).pop();
          },
          // secondaryButtonText: 'Cancel',
          // onSecondaryPressed: () => Navigator.of(context).pop(),
        ),
      );
    // showDialog(
    //   context: context,
    //   builder: (BuildContext context) {

    //     return AlertDialog(
    //       title: Text(context.tr('chat.microphonePermissionRequired')),
    //       content: Text(context.tr('chat.microphonePermissionMessage')),
    //       actions: [
    //         TextButton(
    //           onPressed: () => Navigator.of(context).pop(),
    //           child: Text(context.tr('common.ok')),
    //         ),
    //       ],
    //     );
    //   },
    // );
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface.withAlpha(245),
      body: Row(
        children: [
          // Navigation Sidebar
          // const WebNavigationSidebar(currentScreen: 'chat'),

          // Left Sidebar (Conversations List)
          Container(
            width: 300,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface.withAlpha(240),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withAlpha(10),
                  blurRadius: 4,
                  offset: const Offset(2, 0),
                ),
              ],
            ),
            child: _buildConversationsList(),
          ),

          // Separator between conversations and chat
          Container(
            width: 1,
            margin: const EdgeInsets.symmetric(vertical: 16.0),
            color: Theme.of(context).dividerColor.withAlpha(25),
          ),

          // Main Chat Area
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withAlpha(250),
                borderRadius: BorderRadius.circular(4.0),
              ),
              child: Column(
                children: [
                  // Chat Header
                  _buildChatHeader(),

                  // Messages Area
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 8.0),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(12.0),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).shadowColor.withAlpha(5),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: _buildMessagesArea(),
                    ),
                  ),

                  // Chat Input
                  _buildChatInput(),
                ],
              ),
            ),
          ),

          // Separator between chat and context
          Container(
            width: 1,
            margin: const EdgeInsets.symmetric(vertical: 16.0),
            color: Theme.of(context).dividerColor.withAlpha(25),
          ),

          // Right Sidebar (Context Panel)
          Container(
            width: 300,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface.withAlpha(240),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withAlpha(10),
                  blurRadius: 4,
                  offset: const Offset(-2, 0),
                ),
              ],
            ),
            child: _buildContextPanel(),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationsList() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, _) {
        return Column(
          children: [
            // Conversations Header
            Container(
              height: 64,
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withAlpha(10),
                    blurRadius: 2,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Text(
                    context.tr('chat.conversations'),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 4.0),
                    decoration: BoxDecoration(
                      color:
                          Theme.of(context).colorScheme.secondary.withAlpha(30),
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    child: Text(
                      context.tr('chat.history'),
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Search and New Chat Container
            Container(
              margin: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12.0),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withAlpha(5),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  children: [
                    // Search Bar
                    TextField(
                      decoration: InputDecoration(
                        hintText: context.tr('chat.searchConversations'),
                        prefixIcon: const Icon(Icons.search, size: 20),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Theme.of(context)
                            .colorScheme
                            .surface
                            .withAlpha(240),
                        contentPadding:
                            const EdgeInsets.symmetric(vertical: 0.0),
                        isDense: true,
                      ),
                      style: const TextStyle(fontSize: 14),
                    ),

                    const SizedBox(height: 12),

                    // New Chat Button
                    ElevatedButton.icon(
                      onPressed: () {
                        chatProvider.createNewConversation();
                      },
                      icon: const Icon(Icons.add, size: 18),
                      label: Text(context.tr('chat.newChat')),
                      style: ElevatedButton.styleFrom(
                        minimumSize: const Size.fromHeight(40),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor:
                            Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Conversations List
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12.0),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).shadowColor.withAlpha(5),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Row(
                        children: [
                          Text(
                            context.tr('chat.recentChats'),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            context.tr('chat.chatCount', args: {
                              'count': '${chatProvider.conversations.length}'
                            }),
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Divider(height: 1),
                    Expanded(
                      child: chatProvider.conversations.isEmpty
                          ? Center(
                              child: Text(context.tr('chat.noConversations')))
                          : ClipRRect(
                              borderRadius: BorderRadius.circular(12.0),
                              child: ListView.separated(
                                padding: EdgeInsets.zero,
                                itemCount: chatProvider.conversations.length,
                                separatorBuilder: (context, index) =>
                                    const Divider(height: 1),
                                itemBuilder: (context, index) {
                                  final conversation =
                                      chatProvider.conversations[index];
                                  return _ConversationListItem(
                                    title: conversation.title,
                                    preview:
                                        conversation.lastMessagePreview.isEmpty
                                            ? context.tr('chat.noMessagesYet')
                                            : conversation.lastMessagePreview,
                                    isSelected: conversation.id ==
                                        chatProvider.currentConversationId,
                                    onTap: () {
                                      chatProvider
                                          .selectConversation(conversation.id);
                                    },
                                    onDelete: () {
                                      chatProvider
                                          .deleteConversation(conversation.id);
                                    },
                                  );
                                },
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildChatHeader() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, _) {
        // Find the current conversation
        final currentConversation = chatProvider.conversations.firstWhere(
          (c) => c.id == chatProvider.currentConversationId,
          orElse: () => Conversation(
            id: '',
            title: context.tr('chat.newConversation'),
            lastMessagePreview: '',
            timestamp: DateTime.now(),
          ),
        );

        return Container(
          height: 64,
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).shadowColor.withAlpha(10),
                blurRadius: 2,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Text(
                currentConversation.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Builder(
                builder: (BuildContext menuContext) {
                  return IconButton(
                    icon: const Icon(Icons.more_vert),
                    onPressed: () {
                      // Get the render box of the button
                      final RenderBox button =
                          menuContext.findRenderObject() as RenderBox;
                      final RenderBox overlay = Overlay.of(menuContext)
                          .context
                          .findRenderObject() as RenderBox;
                      // Calculate position to show menu below the button without overlapping
                      final buttonPos =
                          button.localToGlobal(Offset.zero, ancestor: overlay);
                      final buttonSize = button.size;

                      // Position the menu so it appears directly below the button
                      final RelativeRect position = RelativeRect.fromLTRB(
                        buttonPos.dx, // Left
                        buttonPos.dy +
                            buttonSize.height, // Top (below the button)
                        overlay.size.width -
                            buttonPos.dx -
                            buttonSize.width, // Right
                        overlay.size.height -
                            buttonPos.dy -
                            buttonSize.height, // Bottom
                      );

                      // Show chat options menu below the button
                      showMenu(
                        context: menuContext,
                        position: position,
                        items: [
                          PopupMenuItem(
                            child: Row(
                              children: [
                                Icon(Icons.delete_sweep_outlined,
                                    size: 18,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface),
                                const SizedBox(width: 8),
                                Text(context.tr('chat.clearChat')),
                              ],
                            ),
                            onTap: () {
                              chatProvider.clearChat();
                            },
                          ),
                          PopupMenuItem(
                            child: Row(
                              children: [
                                Icon(Icons.edit_outlined,
                                    size: 18,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface),
                                const SizedBox(width: 8),
                                Text(context.tr('chat.rename')),
                              ],
                            ),
                            onTap: () {
                              // Show rename dialog after menu closes
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                if (mounted) {
                                  _showRenameDialog(context, chatProvider,
                                      currentConversation.id);
                                }
                              });
                            },
                          ),
                          PopupMenuItem(
                            child: Row(
                              children: [
                                Icon(Icons.download_outlined,
                                    size: 18,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurface),
                                const SizedBox(width: 8),
                                Text(context.tr('chat.exportChat')),
                              ],
                            ),
                            onTap: () {
                              // Export chat functionality would go here
                            },
                          ),
                        ],
                      );
                    },
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showRenameDialog(
      BuildContext context, ChatProvider chatProvider, String conversationId) {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('chat.renameConversation')),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: context.tr('chat.enterNewName'),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('common.cancel')),
          ),
          TextButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                chatProvider.renameConversation(
                    conversationId, controller.text);
                Navigator.pop(context);
              }
            },
            child: Text(context.tr('chat.rename')),
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesArea() {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, _) {
        if (chatProvider.messages.isEmpty && !chatProvider.isLoading) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  context.tr('chat.greeting', args: {
                    'greeting': GreetingHelper.getTimeBasedGreeting()
                  }),
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Wrap(
                  spacing: 8.0,
                  children: [
                    _SuggestionChip(
                      label: context.tr('chat.suggestions.codeReview'),
                      onTap: () => _handleSuggestionTap(
                          context.tr('chat.suggestions.codeReview')),
                    ),
                    _SuggestionChip(
                      label: context.tr('chat.suggestions.explainConcept'),
                      onTap: () => _handleSuggestionTap(
                          context.tr('chat.suggestions.explainConcept')),
                    ),
                    _SuggestionChip(
                      label: context.tr('chat.suggestions.debugCode'),
                      onTap: () => _handleSuggestionTap(
                          context.tr('chat.suggestions.debugCode')),
                    ),
                  ],
                ),
              ],
            ),
          );
        }

        _scrollToBottom();
        return ClipRRect(
          borderRadius: BorderRadius.circular(12.0),
          child: ListView.builder(
            controller: _scrollController,
            padding:
                const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
            itemCount:
                chatProvider.messages.length + (chatProvider.isLoading ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == chatProvider.messages.length) {
                return const Padding(
                  padding: EdgeInsets.only(top: 16.0),
                  child: Center(child: CircularProgressIndicator()),
                );
              }

              final message = chatProvider.messages[index];
              if (message.role == MessageRole.assistant) {
                return ChatNSLBubble(
                  message: message,
                  onLongPress: () => _speakMessage(message.content),
                );
              } else {
                return FullWidthUserBubble(
                  message: message,
                  onLongPress: () => _speakMessage(message.content),
                );
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildChatInput() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ChatTextField(
        controller: _textController,
        hintText: context.tr('chat.typeMessage'),
        isLoading: context.watch<ChatProvider>().isLoading,
        onSubmitted: (value) {
          if (value.trim().isNotEmpty) {
            context.read<ChatProvider>().sendMessage(value);
            _textController.clear();
            _focusNode.requestFocus();
          }
        },
        onCancel: () => context.read<ChatProvider>().cancelRequest(),
      ),
    );
  }

  Widget _buildContextPanel() {
    return Column(
      children: [
        Container(
          height: 64,
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 2,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Text(
                context.tr('chat.context'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withAlpha(30),
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Text(
                  context.tr('chat.aiAssistant'),
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Container(
            margin: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(5),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('chat.currentConversation'),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12.0),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .primary
                              .withAlpha(15),
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(
                            color: Theme.of(context)
                                .colorScheme
                                .primary
                                .withAlpha(30),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              context.tr('chat.topic'),
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 12),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              context.tr('chat.noSpecificTopic'),
                              style: const TextStyle(fontSize: 12),
                            ),
                            const Divider(),
                            Text(
                              context.tr('chat.keyPoints'),
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 12),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              context.tr('chat.noContextAvailable'),
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('chat.relatedInformation'),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        context.tr('chat.noRelatedInformation'),
                        style: const TextStyle(
                            fontStyle: FontStyle.italic, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _handleSuggestionTap(String suggestion) {
    _textController.text = suggestion;
    _focusNode.requestFocus();
  }
}

class _ConversationListItem extends StatelessWidget {
  final String title;
  final String preview;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback onDelete;

  const _ConversationListItem({
    required this.title,
    required this.preview,
    required this.isSelected,
    required this.onTap,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: isSelected
            ? Theme.of(context).primaryColor.withAlpha(25)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8.0),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
      child: ListTile(
        selected: isSelected,
        selectedTileColor: Colors.transparent,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 12.0, vertical: 4.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        leading: CircleAvatar(
          radius: 18,
          backgroundColor: isSelected
              ? Theme.of(context).primaryColor.withAlpha(50)
              : Theme.of(context).colorScheme.surface,
          child: Icon(
            Icons.chat_outlined,
            size: 16,
            color: isSelected
                ? Theme.of(context).primaryColor
                : Theme.of(context).iconTheme.color,
          ),
        ),
        title: Text(
          title,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 14,
          ),
        ),
        subtitle: Text(
          preview,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).textTheme.bodySmall?.color,
          ),
        ),
        onTap: onTap,
        trailing: Builder(
          builder: (BuildContext menuContext) {
            return IconButton(
              icon: const Icon(Icons.more_horiz, size: 18),
              onPressed: () {
                // Get the render box of the button
                final RenderBox button =
                    menuContext.findRenderObject() as RenderBox;
                final RenderBox overlay = Overlay.of(menuContext)
                    .context
                    .findRenderObject() as RenderBox;
                // Calculate position to show menu below the button without overlapping
                final buttonPos =
                    button.localToGlobal(Offset.zero, ancestor: overlay);
                final buttonSize = button.size;

                // Position the menu so it appears directly below the button
                final RelativeRect position = RelativeRect.fromLTRB(
                  buttonPos.dx, // Left
                  buttonPos.dy + buttonSize.height, // Top (below the button)
                  overlay.size.width - buttonPos.dx - buttonSize.width, // Right
                  overlay.size.height -
                      buttonPos.dy -
                      buttonSize.height, // Bottom
                );

                // Show options menu below the button
                showMenu(
                  context: menuContext,
                  position: position,
                  items: [
                    PopupMenuItem(
                      child: Row(
                        children: [
                          Icon(Icons.edit_outlined,
                              size: 18,
                              color: Theme.of(menuContext).iconTheme.color),
                          const SizedBox(width: 8),
                          Text(context.tr('chat.rename')),
                        ],
                      ),
                      onTap: () {
                        // Delay to allow menu to close
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          // Store context in a local variable to avoid using it across async gaps
                          final BuildContext currentContext = menuContext;
                          if (currentContext.mounted) {
                            showDialog(
                              context: currentContext,
                              builder: (context) => AlertDialog(
                                title:
                                    Text(context.tr('chat.renameConversation')),
                                content: TextField(
                                  autofocus: true,
                                  decoration: InputDecoration(
                                    hintText: context.tr('chat.enterNewName'),
                                  ),
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: Text(context.tr('common.cancel')),
                                  ),
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: Text(context.tr('chat.rename')),
                                  ),
                                ],
                              ),
                            );
                          }
                        });
                      },
                    ),
                    PopupMenuItem(
                      onTap: onDelete,
                      child: Row(
                        children: [
                          Icon(Icons.delete_outline,
                              size: 18,
                              color: Theme.of(menuContext).colorScheme.error),
                          const SizedBox(width: 8),
                          Text(context.tr('common.delete'),
                              style: TextStyle(
                                  color:
                                      Theme.of(menuContext).colorScheme.error)),
                        ],
                      ),
                    ),
                  ],
                );
              },
              tooltip: 'More options',
              visualDensity: VisualDensity.compact,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            );
          },
        ),
      ),
    );
  }
}

class _SuggestionChip extends StatelessWidget {
  final String label;
  final VoidCallback onTap;

  const _SuggestionChip({
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ActionChip(
      label: Text(label),
      onPressed: onTap,
      backgroundColor: Theme.of(context).colorScheme.primary.withAlpha(25),
      labelStyle: TextStyle(color: Theme.of(context).colorScheme.primary),
    );
  }
}
