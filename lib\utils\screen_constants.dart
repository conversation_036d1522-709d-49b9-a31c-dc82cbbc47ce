/// Constants for screen names used throughout the application.
///
/// This class provides centralized access to screen name constants,
/// making it easier to maintain and update screen names in one place.
class ScreenConstants {
  // Private constructor to prevent instantiation
  ScreenConstants._();

  // Home screen
  static const String home = 'home';

  // Create screen
  static const String create = 'create';

  // My Business screen
  static const String myBusiness = 'my_business';

  // My Transactions screen
  static const String myTransactions = 'my_transactions';

  // Calendar screen
  static const String calendar = 'calendar';

  // Notifications screen
  static const String notifications = 'notifications';

  // NSL to Java Code screen
  static const String nslJava = 'nsl_java';

  // My Library screen (commented out in current implementation)
  static const String myLibrary = 'my_library';

  static const String webMyLibrary = 'web_my_library';
  static const String webMySolution = 'web_my_solution';
  static const String webMyObject = 'web_my_object';
  static const String aiGeneratedObject = 'ai_generated_object';
  static const String manualGenerationSolution = 'manual_generation_solution';

  static const String webBookSolution = "web_book_solution_page";
  static const String webBookDetailPage = "web_book_detail_page";

  static const String myBusinessHome = 'my_business_home';
  static const String myBusinessCollections = 'my_business_collections';
  static const String myBusinessSolutions = 'my_business_solutions';
  static const String myBusinessRecords = 'my_business_records';
  static const String myBusinessCollectionsModule =
      'my_business_collections_module';

  static const String webTransactionWidgetsDemo =
      'web_transaction_widgets_demo';

  static const String tempWebChat = 'temp_web_chat';
  // For Mobile screens
  static const String createBookMobile = 'create_book_mobile';
  static const String booksLibraryMobile = 'books_library_mobile';
  static const String solutionsLibraryMobile = 'solutions_library_mobile';
  static const String objectsLibraryMobile = 'objects_library_mobile';
  static const String addModulesMobile = 'add_modules_mobile';
}
