import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/new_design/my_library_mobile/books_library_mobile.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';

/// Mobile version of the Create Book screen
/// This screen allows users to create new books/projects on mobile devices
class CreateBookMobile extends StatefulWidget {
  final Map<String, dynamic>? initialData;

  const CreateBookMobile({
    super.key,
    this.initialData,
  });

  @override
  State<CreateBookMobile> createState() => _CreateBookMobileState();
}

class _CreateBookMobileState extends State<CreateBookMobile> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  // Industry dropdown options
  final List<String> _industries = [
    'E-commerce',
    'Healthcare',
    'Finance',
    'Education',
    'Technology'
  ];
  String? _selectedIndustry;

  @override
  void initState() {
    super.initState();
    // Initialize with data if provided
    if (widget.initialData != null) {
      _nameController.text = widget.initialData!['name'] ?? '';
      _descriptionController.text = widget.initialData!['description'] ?? '';
      _selectedIndustry = widget.initialData!['industry'];
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Always return the create book view directly since navigation is handled by MobileNavigationWrapper
    return _buildCreateBookView();
  }

  // Extract the original build content into a separate method
  Widget _buildCreateBookView() {
    return Scaffold(
      drawer: const CustomDrawer(),
      appBar: AppBar(
        backgroundColor: Color(0xfff6f6f6),
        surfaceTintColor: Colors.transparent,
        foregroundColor: Colors.black,
        elevation: 0,
        automaticallyImplyLeading: false,
        titleSpacing: 0,
        title: Row(
          children: [
            // Hamburger menu icon
            Builder(
              builder: (context) => IconButton(
                icon: const Icon(Icons.menu, color: Colors.black, size: 24),
                onPressed: () => Scaffold.of(context).openDrawer(),
                padding: const EdgeInsets.symmetric(horizontal: 16),
              ),
            ),
            // Expanded widget to center the title
            Expanded(
              child: Text(
                AppLocalizations.of(context)
                    .translate('bookdetails.createYourBook'),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'TiemposText',
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            // Invisible spacer to balance the layout (same width as menu icon)
            const SizedBox(width: 56), // IconButton default width
          ],
        ),
      ),
      backgroundColor: Color(0xfff6f6f6),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(28.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 40),

                // Project Name Field
                Text(
                  AppLocalizations.of(context).translate('bookdetails.name'),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'TiemposText',
                    color: Color(0xff000000),
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 16),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Color(0xffCCCCCC),
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Color(0xffCCCCCC),
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: const BorderSide(
                        color: Color(0xff0058FF),
                        width: 1,
                      ),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a project name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Industry Dropdown Field
                Text(
                  AppLocalizations.of(context)
                      .translate('bookdetails.industry'),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'TiemposText',
                    color: Color(0xff000000),
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _selectedIndustry,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 16),
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Color(0xffCCCCCC),
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Color(0xffCCCCCC),
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: const BorderSide(
                        color: Color(0xff0058FF),
                        width: 1,
                      ),
                    ),
                  ),
                  hint: Text(
                    AppLocalizations.of(context)
                        .translate('bookdetails.industry'),
                    style: const TextStyle(
                      fontSize: 14,
                      fontFamily: 'TiemposText',
                    ),
                  ),
                  isExpanded: true,
                  icon: const Icon(Icons.arrow_drop_down),
                  items: _industries.map((String industry) {
                    return DropdownMenuItem<String>(
                      value: industry,
                      child: Text(
                        industry,
                        style: const TextStyle(
                          fontSize: 14,
                          fontFamily: 'TiemposText',
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedIndustry = newValue;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please select an industry';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Description Field
                Text(
                  AppLocalizations.of(context)
                      .translate('bookdetails.descriptionAboutTheProject'),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'TiemposText',
                    color: Color(0xff000000),
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _descriptionController,
                  maxLines: 4,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 16),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Color(0xffCCCCCC),
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: BorderSide(
                        color: Color(0xffCCCCCC),
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6),
                      borderSide: const BorderSide(
                        color: Color(0xff0058FF),
                        width: 1,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 40),

                // Start Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        // Create a data object with form values
                        final bookData = {
                          'name': _nameController.text,
                          'description': _descriptionController.text,
                          'industry': _selectedIndustry,
                        };

                        // Navigate to BooksLibraryMobile using traditional navigation
                        debugPrint('Book Data: $bookData');
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const BooksLibraryMobile(),
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xff0058FF),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)
                          .translate('bookdetails.start'),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
