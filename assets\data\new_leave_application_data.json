{"nsl_file_name": "LeaveApplication.nsl", "java_file_name": "LeaveApplication.java", "prescriptives": 0, "sub_prescriptives": 0, "BETs": 0, "pathways": 0, "code": [{"line": 1, "code": "public class LeaveApplication {"}, {"line": 2, "code": "    private static int idCounter = 1;"}, {"line": 3, "code": "    private static final int DEFAULT_BALANCE = 30;"}, {"line": 4, "code": "    private static final int MAX_CONSECUTIVE_DAYS = 15;"}, {"line": 5, "code": "    private String leaveId;"}, {"line": 6, "code": "    private String employeeId;"}, {"line": 7, "code": "    private String employee<PERSON>ame;"}, {"line": 8, "code": "    private Date startDate;"}, {"line": 9, "code": "    private Date endDate;"}, {"line": 10, "code": "    private int numberOfDays;"}, {"line": 11, "code": "    private LeaveType leaveType;"}, {"line": 12, "code": "    private String reason;"}, {"line": 13, "code": "    private ApplicationStatus status;"}, {"line": 14, "code": "    private int availableBalance;"}, {"line": 15, "code": "    enum LeaveType { SICK, CASUAL, ANNUAL, MATERNITY }"}, {"line": 16, "code": "    enum ApplicationStatus { PENDING, APPROVED, REJECTED }"}, {"line": 17, "code": "    private String generateLeaveId() {"}, {"line": 18, "code": "        String leaveId = \"lv\" + idCounter;"}, {"line": 19, "code": "        idCounter++;"}, {"line": 20, "code": "        return leaveId;"}, {"line": 21, "code": "    }"}, {"line": 22, "code": "    private int calculateNumberOfDays(Date start, Date end) {"}, {"line": 23, "code": "        long diff = end.getTime() - start.getTime();"}, {"line": 24, "code": "        return (int)(diff / (1000*60*60*24)) + 1;"}, {"line": 25, "code": "    }"}, {"line": 26, "code": "    public LeaveApplication(String employeeId, String employeeName, Date startDate, Date endDate, LeaveType leaveType, String reason) {"}, {"line": 27, "code": "        this.leaveId = generateLeaveId();"}, {"line": 28, "code": "        this.employeeId = employeeId;"}, {"line": 29, "code": "        if (employeeId == null || employeeId.isEmpty()) throw new ValidationException(\"employee id required\");"}, {"line": 30, "code": "        this.employeeName = employeeName;"}, {"line": 31, "code": "        if (employeeName == null || employeeName.isEmpty()) throw new ValidationException(\"employee name required\");"}, {"line": 32, "code": "        this.startDate = startDate;"}, {"line": 33, "code": "        if (startDate == null) throw new ValidationException(\"start date required\");"}, {"line": 34, "code": "        this.endDate = endDate;"}, {"line": 35, "code": "        if (endDate == null) throw new ValidationException(\"end date required\");"}, {"line": 36, "code": "        this.numberOfDays = calculateNumberOfDays(startDate, endDate);"}, {"line": 37, "code": "        this.leaveType = leaveType;"}, {"line": 38, "code": "        if (leaveType == null) throw new ValidationException(\"leave type required\");"}, {"line": 39, "code": "        this.reason = reason;"}, {"line": 40, "code": "        if (reason == null || reason.isEmpty()) throw new ValidationException(\"reason required\");"}, {"line": 41, "code": "        this.status = ApplicationStatus.PENDING;"}, {"line": 42, "code": "        this.availableBalance = DEFAULT_BALANCE;"}, {"line": 43, "code": "        if (USED_LEAVE_IDS.contains(leaveId)) {"}, {"line": 44, "code": "            throw new ValidationException(\"duplicate leave id\");"}, {"line": 45, "code": "        }"}, {"line": 46, "code": "        if (!VALID_EMPLOYEE_IDS.contains(employeeId)) {"}, {"line": 47, "code": "            throw new ValidationException(\"invalid employee id\");"}, {"line": 48, "code": "        }"}, {"line": 49, "code": "        if (status != ApplicationStatus.PENDING) {"}, {"line": 50, "code": "            throw new ValidationException(\"invalid status\");"}, {"line": 51, "code": "        }"}, {"line": 52, "code": "    }"}, {"line": 53, "code": "    public boolean submit() {"}, {"line": 54, "code": "        boolean isValid = validateApplication();"}, {"line": 55, "code": "        boolean requiresManagerReview = determineWorkflowPath();"}, {"line": 56, "code": "        return isValid && requiresManagerReview;"}, {"line": 57, "code": "    }"}, {"line": 58, "code": "    private boolean validateApplication() {"}, {"line": 59, "code": "        return true;"}, {"line": 60, "code": "    }"}, {"line": 61, "code": "    private boolean determineWorkflowPath() {"}, {"line": 62, "code": "        boolean requiresManagerReview = false;"}, {"line": 63, "code": "        if (numberOfDays > MAX_CONSECUTIVE_DAYS) {"}, {"line": 64, "code": "            requiresManagerReview = true;"}, {"line": 65, "code": "        }"}, {"line": 66, "code": "        if (numberOfDays <= MAX_CONSECUTIVE_DAYS) {"}, {"line": 67, "code": "            requiresManagerReview = false;"}, {"line": 68, "code": "        }"}, {"line": 69, "code": "        return requiresManagerReview;"}, {"line": 70, "code": "    }"}, {"line": 71, "code": "    public String getLeaveId() { return leaveId; }"}, {"line": 72, "code": "    public String getEmployeeId() { return employeeId; }"}, {"line": 73, "code": "    public String getEmployeeName() { return employeeName; }"}, {"line": 74, "code": "    public Date getStartDate() { return startDate; }"}, {"line": 75, "code": "    public Date getEndDate() { return endDate; }"}, {"line": 76, "code": "    public int getNumberOfDays() { return numberOfDays; }"}, {"line": 77, "code": "    public LeaveType getLeaveType() { return leaveType; }"}, {"line": 78, "code": "    public String getReason() { return reason; }"}, {"line": 79, "code": "    public ApplicationStatus getStatus() { return status; }"}, {"line": 80, "code": "    public int getAvailableBalance() { return availableBalance; }"}, {"line": 81, "code": "    public static void main(String[] args) throws Exception {"}, {"line": 82, "code": "        Scanner scanner = new Scanner(System.in);"}, {"line": 83, "code": "        System.out.println(\"Leave ID: \" + \"Auto-generated\");"}, {"line": 84, "code": "        System.out.print(\"Enter Employee ID: \");"}, {"line": 85, "code": "        String employeeId = scanner.nextLine();"}, {"line": 86, "code": "        System.out.print(\"Enter Employee Name: \");"}, {"line": 87, "code": "        String employeeName = scanner.nextLine();"}, {"line": 88, "code": "        System.out.print(\"Enter Start Date (yyyy-MM-dd): \");"}, {"line": 89, "code": "        String startDateStr = scanner.nextLine();"}, {"line": 90, "code": "        Date startDate = new SimpleDateFormat(\"yyyy-MM-dd\").parse(startDateStr);"}, {"line": 91, "code": "        System.out.print(\"Enter End Date (yyyy-MM-dd): \");"}, {"line": 92, "code": "        String endDateStr = scanner.nextLine();"}, {"line": 93, "code": "        Date endDate = new SimpleDateFormat(\"yyyy-MM-dd\").parse(endDateStr);"}, {"line": 94, "code": "        System.out.println(\"Select Leave Type:\");"}, {"line": 95, "code": "        System.out.println(\"1. SICK\");"}, {"line": 96, "code": "        System.out.println(\"2. CASUAL\");"}, {"line": 97, "code": "        System.out.println(\"3. ANNUAL\");"}, {"line": 98, "code": "        System.out.println(\"4. MATERNITY\");"}, {"line": 99, "code": "        int choice = scanner.nextInt();"}, {"line": 100, "code": "        scanner.nextLine(); // consume newline"}, {"line": 101, "code": "        LeaveType leaveType = LeaveType.values()[choice-1];"}, {"line": 102, "code": "        System.out.print(\"Enter Reason for Leave: \");"}, {"line": 103, "code": "        String reason = scanner.nextLine();"}, {"line": 104, "code": "        if (user != null && user.hasRole(\"EMPLOYEE\")) {"}, {"line": 105, "code": "            LeaveApplication application = new LeaveApplication(employeeId, employeeName, startDate, endDate, leaveType, reason);"}, {"line": 106, "code": "            System.out.println(\"Number of Days: \" + application.getNumberOfDays());"}, {"line": 107, "code": "            System.out.println(\"Available Balance: \" + application.getAvailableBalance());"}, {"line": 108, "code": "            boolean submitted = application.submit();"}, {"line": 109, "code": "            if (submitted) {"}, {"line": 110, "code": "                LeaveReview managerReview = new LeaveReview();"}, {"line": 111, "code": "                managerReview.reviewApplication(application, ApplicationStatus.APPROVED);"}, {"line": 112, "code": "            }"}, {"line": 113, "code": "        } else {"}, {"line": 114, "code": "            throw new AccessDeniedException(\"Only employees can apply for leave\");"}, {"line": 115, "code": "        }"}, {"line": 116, "code": "    }"}, {"line": 117, "code": "}"}, {"line": 118, "code": "class ValidationException extends RuntimeException {"}, {"line": 119, "code": "    public ValidationException(String message) {"}, {"line": 120, "code": "        super(message);"}, {"line": 121, "code": "    }"}, {"line": 122, "code": "}"}, {"line": 123, "code": "class AccessDeniedException extends RuntimeException {"}, {"line": 124, "code": "    public AccessDeniedException(String message) {"}, {"line": 125, "code": "        super(message);"}, {"line": 126, "code": "    }"}, {"line": 127, "code": "}"}, {"line": 128, "code": "class LeaveReview {"}, {"line": 129, "code": "    public void reviewApplication(LeaveApplication application, ApplicationStatus status) {"}, {"line": 130, "code": "    }"}, {"line": 131, "code": "}"}], "stacks": [{"stack_name": "Primary Function", "steps": [{"id": "P1", "sentence_id": 1, "sentence": "Applies for leave.", "related_lines": [1, 117], "related_keywords": [{"keyword": "public", "usage": "public is like making something available to everyone - it means any part of the program can use this code, similar to a public library where anyone can access the books."}, {"keyword": "class", "usage": "class is like a blueprint for creating objects - it defines what properties and behaviors a thing should have, similar to how a house blueprint defines rooms and features."}, {"keyword": "{", "usage": "Opening curly brace is like the 'start here' marker for a section of code - it's similar to opening a book to begin reading a chapter."}, {"keyword": "}", "usage": "Closing curly brace is like the 'end here' marker for a section of code - it's similar to reaching the end of a chapter in a book."}, {"keyword": "LeaveApplication", "usage": "LeaveApplication is the main container for all the leave request information - like a digital form that holds all the details about an employee's time-off request."}], "color": "#FFD700"}]}, {"stack_name": "Agent <PERSON>", "steps": [{"id": "AG1", "sentence_id": 2, "sentence": "Employee has execution rights to apply for leave.", "related_lines": [104, 105, 113, 114, 115, 123, 124, 125, 126, 127], "related_keywords": [{"keyword": "if", "usage": "if is like a decision gate - it checks if something is true and only proceeds with certain actions when it is, similar to saying 'if it's raining, take an umbrella'."}, {"keyword": "else", "usage": "else is like the alternative path - it tells the program what to do when the if condition isn't true, similar to saying 'if it's raining, take an umbrella, else wear sunglasses'."}, {"keyword": "throw", "usage": "throw is like raising a red flag when something goes wrong - it stops normal program flow and signals an error, similar to how a referee throws a flag to signal a foul in sports."}, {"keyword": "class", "usage": "class is a keyword used to define a class in Java."}, {"keyword": "new", "usage": "new is like a magic word that creates actual objects from blueprints - it's similar to using a cookie cutter (the class) to make a real cookie (the object) that you can use."}, {"keyword": "&&", "usage": "&& is the AND operator - it checks if two conditions are BOTH true, like saying 'I'll go to the beach if it's sunny AND it's warm' - both conditions must be true for the whole statement to be true."}, {"keyword": "||", "usage": "|| is the OR operator - it checks if at least one of two conditions is true, like saying 'I'll take an umbrella if it's raining OR it looks cloudy' - only one condition needs to be true."}, {"keyword": "==", "usage": "== is the equality operator - it checks if two values are exactly the same, like asking 'Is this apple the same as that apple?' It returns true only when both sides match exactly."}, {"keyword": "!=", "usage": "!= is the not-equal operator - it checks if two values are different from each other, like asking 'Is this apple different from that orange?' It returns true when the items don't match."}, {"keyword": "null", "usage": "null is like an empty box - it represents the absence of a value or an object that doesn't exist yet, similar to having a variable named 'car' but you don't own a car yet."}, {"keyword": ".", "usage": "The dot (.) is like a pathway to access things inside an object - similar to how you might say 'car.color' to refer to the color property of your car."}, {"keyword": "=", "usage": "The equals sign (=) is used to store a value in a variable - it's like putting something in a labeled container, such as saying 'temperature = 75' to store the value 75 in the temperature variable."}, {"keyword": "super", "usage": "super is like calling your parent for help - it lets a child class access methods and properties from its parent class, similar to how you might inherit traits from your parents."}, {"keyword": "String", "usage": "String is a type that holds text data - it's like a container specifically designed for storing words, sentences, or any sequence of characters like 'Hello World' or '<PERSON>'."}, {"keyword": ";", "usage": "The semicolon (;) is like a period at the end of a sentence - it tells Java that you've finished a complete instruction, similar to how a period marks the end of a sentence in English."}, {"keyword": "RuntimeException", "usage": "RuntimeException is like an unexpected problem that can occur while the program is running - similar to a car breaking down while you're driving it, rather than finding a problem during a pre-trip inspection."}], "color": "#87CEFA"}]}, {"stack_name": "Input Stack", "steps": [{"id": "IN1", "sentence_id": 3, "sentence": "System auto-generates Leave ID with prefix \"lv\".", "related_lines": [2, 5, 17, 18, 19, 20, 21, 27, 71], "related_keywords": [{"keyword": "private", "usage": "private is like a 'keep out' sign - it restricts access to certain parts of the code so only the class itself can use them, similar to having a private diary that only you can read."}, {"keyword": "static", "usage": "static is like a community bulletin board - it creates variables and methods that belong to the entire class rather than to individual objects, similar to how a town library belongs to the whole town, not just one person."}, {"keyword": "int", "usage": "int is a type that stores whole numbers - it's like a special container that can only hold numbers without decimal points, such as 1, 42, or -273."}, {"keyword": "return", "usage": "return is like finishing a task and reporting back - it exits a method and optionally sends back a result, similar to how you might complete an errand and bring back what you were sent for."}, {"keyword": "=", "usage": "Assignment operator that assigns a value to a variable."}, {"keyword": "+", "usage": "The plus sign (+) has two jobs - it can add numbers together (like 2+2=4) or it can join strings of text (like 'Hello' + 'World' = 'HelloWorld'), similar to how you might combine ingredients when cooking."}, {"keyword": "++", "usage": "The increment operator (++) is a shortcut to add 1 to a number - it's like having a counter that you click to increase by one, such as changing 'count = count + 1' to the simpler 'count++'."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses () serve two main purposes - they contain the inputs for methods (like giving ingredients to a chef) and they control the order of operations in calculations (like in math when you calculate what's in parentheses first)."}, {"keyword": "this", "usage": "this is like saying 'myself' - it's how an object refers to itself, similar to how you might point to yourself when saying 'I'll do this task myself' rather than asking someone else."}], "color": "#90EE90"}, {"id": "IN2", "sentence_id": 4, "sentence": "Employee ID.", "related_lines": [6, 28, 29, 72], "related_keywords": [{"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "||", "usage": "Logical OR operator that returns true if either operand is true."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "isEmpty", "usage": "isEmpty is like checking if a container has nothing in it - it's a method that checks if a string has zero characters, similar to looking in a box to see if it's empty."}, {"keyword": "this", "usage": "this is a reference to the current object."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "=", "usage": "Assignment operator that assigns a value to a variable."}, {"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}], "color": "#FFA07A"}, {"id": "IN3", "sentence_id": 5, "sentence": "Employee Name.", "related_lines": [7, 30, 31, 73], "related_keywords": [{"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": "this", "usage": "this is a reference to the current object."}, {"keyword": "=", "usage": "Assignment operator that assigns a value to a variable."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "||", "usage": "Logical OR operator that returns true if either operand is true."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "isEmpty", "usage": "isEmpty is a method that checks if a string is empty."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}], "color": "#FF69B4"}, {"id": "IN4", "sentence_id": 6, "sentence": "Start Date.", "related_lines": [8, 32, 33, 74], "related_keywords": [{"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "Date", "usage": "Date is like a timestamp or calendar entry - it's a type that stores a specific moment in time, similar to marking a date and time on your calendar but with much more precision (down to milliseconds)."}, {"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "||", "usage": "Logical OR operator that returns true if either operand is true."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "this", "usage": "this is a reference to the current object."}], "color": "#20B2AA"}, {"id": "IN5", "sentence_id": 7, "sentence": "End Date.", "related_lines": [9, 34, 35, 75], "related_keywords": [{"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "Date", "usage": "Date is a class that represents a specific instant in time, with millisecond precision."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "||", "usage": "Logical OR operator that returns true if either operand is true."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "this", "usage": "this is a reference to the current object."}], "color": "#9370DB"}, {"id": "IN6", "sentence_id": 8, "sentence": "System calculates Number of Days by subtracting Start Date from End Date.", "related_lines": [10, 22, 23, 24, 25, 36, 76], "related_keywords": [{"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "int", "usage": "int is a primitive data type that stores integer values."}, {"keyword": "Date", "usage": "Date is a class that represents a specific instant in time, with millisecond precision."}, {"keyword": "long", "usage": "long is a primitive data type that stores long integer values."}, {"keyword": "=", "usage": "Assignment operator that assigns a value to a variable."}, {"keyword": "*", "usage": "Multiplication operator that multiplies two values."}, {"keyword": "/", "usage": "Division operator that divides the left operand by the right operand."}, {"keyword": "+", "usage": "Addition operator that adds two values or concatenates strings."}, {"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "this", "usage": "this is a reference to the current object."}], "color": "#32CD32"}, {"id": "IN7", "sentence_id": 9, "sentence": "User selects Leave Type (SICK, CASUAL, ANNUAL, MATERNITY).", "related_lines": [11, 15, 37, 38, 77], "related_keywords": [{"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "enum", "usage": "enum is like a list of preset options - it defines a fixed set of possible values, similar to a restaurant menu with specific meal choices rather than allowing any food to be ordered."}, {"keyword": "this", "usage": "this is a reference to the current object."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}], "color": "#FF7F50"}, {"id": "IN8", "sentence_id": 10, "sentence": "Reason for leave.", "related_lines": [12, 39, 40, 78], "related_keywords": [{"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": "this", "usage": "this is a reference to the current object."}, {"keyword": "=", "usage": "Assignment operator that assigns a value to a variable."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "||", "usage": "Logical OR operator that returns true if either operand is true."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "isEmpty", "usage": "isEmpty is a method that checks if a string is empty."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}], "color": "#6495ED"}, {"id": "IN9", "sentence_id": 11, "sentence": "System defaults Status to \"pending\". (Pending,Approval,Rejected)", "related_lines": [13, 16, 41], "related_keywords": [{"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "enum", "usage": "enum is a special data type that enables for a variable to be a set of predefined constants."}, {"keyword": "this", "usage": "this is a reference to the current object."}, {"keyword": "=", "usage": "Assignment operator that assigns a value to a variable."}, {"keyword": "ApplicationStatus", "usage": "ApplicationStatus is an enum type that represents the different statuses of a leave application."}, {"keyword": "PENDING", "usage": "PENDING is a constant in the ApplicationStatus enum representing a pending status."}, {"keyword": "APPROVED", "usage": "APPROVED is a constant in the ApplicationStatus enum representing an approved status."}, {"keyword": "REJECTED", "usage": "REJECTED is a constant in the ApplicationStatus enum representing a rejected status."}, {"keyword": "status", "usage": "status is a field that stores the status of the leave application."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}], "color": "#DDA0DD"}, {"id": "IN10", "sentence_id": 12, "sentence": "System provides Available Balance, initialized to 30.", "related_lines": [3, 14, 42, 80], "related_keywords": [{"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "static", "usage": "static is a keyword used to create class variables and methods that belong to the class rather than an instance."}, {"keyword": "final", "usage": "final is a keyword used to declare that a variable, method, or class cannot be changed."}, {"keyword": "int", "usage": "int is a primitive data type that stores integer values."}, {"keyword": "this", "usage": "this is a reference to the current object."}, {"keyword": "=", "usage": "Assignment operator that assigns a value to a variable."}, {"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}], "color": "#BDB76B"}, {"id": "IN11", "sentence_id": 47, "sentence": "User submits leave request.", "related_lines": [26, 53, 54, 55, 56, 57, 58, 59, 60], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": "Date", "usage": "Date is a class that represents a specific instant in time, with millisecond precision."}, {"keyword": "boolean", "usage": "boolean is like a simple yes/no switch - it's a type that can only have two possible values (true or false), similar to a light switch that can only be on or off."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "&&", "usage": "Logical AND operator that returns true if both operands are true."}, {"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "submit", "usage": "submit is a method that submits the leave application for processing."}, {"keyword": "validateApplication", "usage": "validateApplication is a method that validates the leave application data."}, {"keyword": "determineWorkflowPath", "usage": "determineWorkflowPath is a method that determines the workflow path for the leave application."}, {"keyword": "<PERSON><PERSON><PERSON><PERSON>", "usage": "isValid is a variable that indicates whether the application is valid."}, {"keyword": "requiresManagerReview", "usage": "requiresManagerReview is a variable that indicates whether the application requires manager review."}, {"keyword": "true", "usage": "true is like saying 'yes' or 'correct' - it's one of the two possible values for a boolean, similar to answering 'yes' to a yes/no question."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#DDA0DD"}]}, {"stack_name": "DB Stack", "steps": [{"id": "DB1", "sentence_id": 13, "sentence": "System verifies Leave ID is unique.", "related_lines": [43, 44, 45], "related_keywords": [{"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "contains", "usage": "contains is a method that checks if a collection contains a specific element."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "{", "usage": "Opening curly brace defines the beginning of a code block in Java."}, {"keyword": "}", "usage": "Closing curly brace defines the end of a code block in Java."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "ValidationException", "usage": "ValidationException is a custom exception class for validation errors."}], "color": "#00CED1"}, {"id": "DB2", "sentence_id": 14, "sentence": "System verifies Employee ID exists.", "related_lines": [46, 47, 48, 118, 119, 120, 121, 122], "related_keywords": [{"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "class", "usage": "class is a keyword used to define a class in Java."}, {"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "super", "usage": "super is a reference variable used to refer to the parent class object."}, {"keyword": "contains", "usage": "contains is like a search function - it checks if a collection (like a list) has a specific item in it, similar to checking if a specific name appears in a guest list."}, {"keyword": "!", "usage": "The exclamation mark (!) is the NOT operator - it flips a true/false value to its opposite, similar to saying 'not' before a statement to reverse its meaning, like changing 'it is raining' to 'it is NOT raining'."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "{", "usage": "Opening curly brace defines the beginning of a code block in Java."}, {"keyword": "}", "usage": "Closing curly brace defines the end of a code block in Java."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "ValidationException", "usage": "ValidationException is a custom exception class for validation errors."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}], "color": "#FF4500"}, {"id": "DB3", "sentence_id": 16, "sentence": "System verifies Status is set to \"pending\".", "related_lines": [49, 50, 51, 118, 119, 120, 121, 122], "related_keywords": [{"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "class", "usage": "class is a keyword used to define a class in Java."}, {"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "super", "usage": "super is a reference variable used to refer to the parent class object."}, {"keyword": "!=", "usage": "Inequality operator that checks if two values are not equal."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "{", "usage": "Opening curly brace defines the beginning of a code block in Java."}, {"keyword": "}", "usage": "Closing curly brace defines the end of a code block in Java."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "ValidationException", "usage": "ValidationException is a custom exception class for validation errors."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}], "color": "#2E8B57"}]}, {"stack_name": "Output Stack", "steps": [{"id": "OU1", "sentence_id": 17, "sentence": "System generates execution status", "related_lines": [53, 54, 55, 56, 57, 128, 129, 130, 131], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "boolean", "usage": "boolean is a primitive data type that can have only two values: true or false."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "class", "usage": "class is a keyword used to define a class in Java."}, {"keyword": "void", "usage": "void is like a label for methods that don't give anything back - it indicates that a method performs an action but doesn't return any value, similar to dropping a letter in a mailbox without expecting a reply."}, {"keyword": "&&", "usage": "Logical AND operator that returns true if both operands are true."}, {"keyword": "submit", "usage": "submit is a method that submits the leave application."}, {"keyword": "validateApplication", "usage": "validateApplication is a method that validates the leave application."}, {"keyword": "determineWorkflowPath", "usage": "determineWorkflowPath is a method that determines the workflow path for the leave application."}, {"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "LeaveReview", "usage": "LeaveReview is a class that handles the review of leave applications."}, {"keyword": "reviewApplication", "usage": "reviewApplication is a method that reviews a leave application."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}], "color": "#DAA520"}, {"id": "OU2", "sentence_id": 18, "sentence": "System captures Leave ID.", "related_lines": [71], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "getLeaveId", "usage": "getLeaveId is a method that returns the leave ID."}, {"keyword": "leaveId", "usage": "leaveId is a field that stores the leave ID."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces {} are like containers that group related code together - they define the boundaries of a block of code, similar to how parentheses in a sentence group related words."}], "color": "#DC143C"}, {"id": "OU3", "sentence_id": 19, "sentence": "System captures Employee ID.", "related_lines": [72], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "getEmployeeId", "usage": "getEmployeeId is a method that returns the employee ID."}, {"keyword": "employeeId", "usage": "employeeId is a field that stores the employee ID."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#4682B4"}, {"id": "OU4", "sentence_id": 20, "sentence": "System captures Employee Name.", "related_lines": [73], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "getEmployeeName", "usage": "getEmployeeName is a method that returns the employee name."}, {"keyword": "employeeName", "usage": "employeeName is a field that stores the employee name."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#008080"}, {"id": "OU5", "sentence_id": 21, "sentence": "System captures Start Date.", "related_lines": [74], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "Date", "usage": "Date is a class that represents a specific instant in time, with millisecond precision."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "getStartDate", "usage": "getStartDate is a method that returns the start date of the leave."}, {"keyword": "startDate", "usage": "startDate is a field that stores the start date of the leave."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#D2691E"}, {"id": "OU6", "sentence_id": 22, "sentence": "System captures End Date.", "related_lines": [75], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "Date", "usage": "Date is a class that represents a specific instant in time, with millisecond precision."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "getEndDate", "usage": "getEndDate is a method that returns the end date of the leave."}, {"keyword": "endDate", "usage": "endDate is a field that stores the end date of the leave."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#9932CC"}, {"id": "OU7", "sentence_id": 23, "sentence": "System captures Number of Days.", "related_lines": [76], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "int", "usage": "int is a primitive data type that stores integer values."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "getNumberOfDays", "usage": "getNumberOfDays is a method that returns the number of days for the leave."}, {"keyword": "numberOfDays", "usage": "numberOfDays is a field that stores the number of days for the leave."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#8FBC8F"}, {"id": "OU8", "sentence_id": 24, "sentence": "System captures Leave Type.", "related_lines": [77], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "LeaveType", "usage": "LeaveType is an enum type that represents the different types of leave."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "getLeaveType", "usage": "getLeaveType is a method that returns the type of leave."}, {"keyword": "leaveType", "usage": "leaveType is a field that stores the type of leave."}, {"keyword": "enum", "usage": "enum is a special data type that enables for a variable to be a set of predefined constants."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#B8860B"}, {"id": "OU9", "sentence_id": 25, "sentence": "System captures Reason.", "related_lines": [78], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "getReason", "usage": "getReason is a method that returns the reason for the leave."}, {"keyword": "reason", "usage": "reason is a field that stores the reason for the leave."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#483D8B"}, {"id": "OU10", "sentence_id": 26, "sentence": "System captures Status.", "related_lines": [79], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "ApplicationStatus", "usage": "ApplicationStatus is an enum type that represents the different statuses of a leave application."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "getStatus", "usage": "getStatus is a method that returns the status of the leave application."}, {"keyword": "status", "usage": "status is a field that stores the status of the leave application."}, {"keyword": "enum", "usage": "enum is a special data type that enables for a variable to be a set of predefined constants."}, {"keyword": "PENDING", "usage": "PENDING is a constant in the ApplicationStatus enum representing a pending status."}, {"keyword": "APPROVED", "usage": "APPROVED is a constant in the ApplicationStatus enum representing an approved status."}, {"keyword": "REJECTED", "usage": "REJECTED is a constant in the ApplicationStatus enum representing a rejected status."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#2F4F4F"}, {"id": "OU11", "sentence_id": 27, "sentence": "System captures Available Balance.", "related_lines": [80], "related_keywords": [{"keyword": "public", "usage": "public is an access modifier that makes a class, method, or field accessible from any other class."}, {"keyword": "int", "usage": "int is a primitive data type that stores integer values."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": "getAvailableBalance", "usage": "getAvailableBalance is a method that returns the available leave balance."}, {"keyword": "availableBalance", "usage": "availableBalance is a field that stores the available leave balance."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#800080"}]}, {"stack_name": "Execution Rules", "steps": [{"id": "ER1", "sentence_id": 28, "sentence": "On successful creation, data is passed to Manager Review function.", "related_lines": [109, 110, 111, 112], "related_keywords": [{"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "{", "usage": "Opening curly brace defines the beginning of a code block in Java."}, {"keyword": "}", "usage": "Closing curly brace defines the end of a code block in Java."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "LeaveReview", "usage": "LeaveReview is a class that handles the review of leave applications."}, {"keyword": "reviewApplication", "usage": "reviewApplication is a method that reviews a leave application."}, {"keyword": "true", "usage": "true is a boolean literal representing the true value."}, {"keyword": "&&", "usage": "Logical AND operator that returns true if both operands are true."}, {"keyword": "determineWorkflowPath", "usage": "determineWorkflowPath is a method that determines the workflow path for the leave application."}], "color": "#FF1493"}, {"id": "ER2", "sentence_id": 29, "sentence": "If Number of Days is greater than 15, workflow proceeds to Manager Review.", "related_lines": [4, 61, 62, 63, 64, 65, 69], "related_keywords": [{"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "static", "usage": "static is a keyword used to create class variables and methods that belong to the class rather than an instance."}, {"keyword": "final", "usage": "final is a keyword used to declare that a variable, method, or class cannot be changed."}, {"keyword": "int", "usage": "int is a primitive data type that stores integer values."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "boolean", "usage": "boolean is a primitive data type that can have only two values: true or false."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": ">", "usage": "Greater than operator that checks if the left operand is greater than the right operand."}, {"keyword": "determineWorkflowPath", "usage": "determineWorkflowPath is a method that determines the workflow path for the leave application."}, {"keyword": "getNumberOfDays", "usage": "getNumberOfDays is a method that returns the number of days for the leave."}, {"keyword": "true", "usage": "true is a boolean literal representing the true value."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#00FF7F"}, {"id": "ER3", "sentence_id": 30, "sentence": "If Number of Days is less than or equal to 15, workflow should not proceed to Manager Review.", "related_lines": [66, 67, 68], "related_keywords": [{"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "boolean", "usage": "boolean is a primitive data type that can have only two values: true or false."}, {"keyword": "<=", "usage": "Less than or equal to operator that checks if the left operand is less than or equal to the right operand."}, {"keyword": "{", "usage": "Opening curly brace defines the beginning of a code block in Java."}, {"keyword": "}", "usage": "Closing curly brace defines the end of a code block in Java."}, {"keyword": "=", "usage": "Assignment operator that assigns a value to a variable."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "determineWorkflowPath", "usage": "determineWorkflowPath is a method that determines the workflow path for the leave application."}, {"keyword": "getNumberOfDays", "usage": "getNumberOfDays is a method that returns the number of days for the leave."}, {"keyword": "false", "usage": "false is a boolean literal representing the false value."}, {"keyword": "return", "usage": "return is used to exit from a method, with or without a value."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}], "color": "#7B68EE"}]}, {"stack_name": "UI Stack", "steps": [{"id": "UI1", "sentence_id": 31, "sentence": "System displays Leave ID as read-only text field.", "related_lines": [83], "related_keywords": [{"keyword": "System", "usage": "System is a final class in the java.lang package that provides access to system resources."}, {"keyword": "println", "usage": "println is a method of PrintStream class that prints the specified data and then terminates the line."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "+", "usage": "Addition operator that adds two values or concatenates strings."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "out", "usage": "out is a static member of the System class that represents the standard output stream."}, {"keyword": "getLeaveId", "usage": "getLeaveId is a method that returns the leave ID."}, {"keyword": "String", "usage": "String is a class that represents character strings."}], "color": "#CD5C5C"}, {"id": "UI2", "sentence_id": 32, "sentence": "System renders Employee ID as text input field.", "related_lines": [84, 85], "related_keywords": [{"keyword": "System", "usage": "System is a final class in the java.lang package that provides access to system resources."}, {"keyword": "print", "usage": "print is a method of PrintStream class that prints the specified data without terminating the line."}, {"keyword": "nextLine", "usage": "nextLine is a method of Scanner class that reads a line of text from the input."}, {"keyword": "out", "usage": "out is a static member of the System class that represents the standard output stream."}, {"keyword": "in", "usage": "in is a static member of the System class that represents the standard input stream."}, {"keyword": "Scanner", "usage": "Scanner is a class that provides methods for reading input of various types."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "employeeId", "usage": "employeeId is a field that stores the employee ID."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}], "color": "#4169E1"}, {"id": "UI3", "sentence_id": 33, "sentence": "System renders Employee Name as text input field.", "related_lines": [86, 87], "related_keywords": [{"keyword": "System", "usage": "System is a final class in the java.lang package that provides access to system resources."}, {"keyword": "print", "usage": "print is a method of PrintStream class that prints the specified data without terminating the line."}, {"keyword": "nextLine", "usage": "nextLine is a method of Scanner class that reads a line of text from the input."}, {"keyword": "out", "usage": "out is a static member of the System class that represents the standard output stream."}, {"keyword": "in", "usage": "in is a static member of the System class that represents the standard input stream."}, {"keyword": "Scanner", "usage": "Scanner is a class that provides methods for reading input of various types."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "employeeName", "usage": "employeeName is a field that stores the employee name."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}], "color": "#808000"}, {"id": "UI4", "sentence_id": 34, "sentence": "System renders Start Date as date picker.", "related_lines": [88, 89, 90], "related_keywords": [{"keyword": "System", "usage": "System is a final class in the java.lang package that provides access to system resources."}, {"keyword": "print", "usage": "print is a method of PrintStream class that prints the specified data without terminating the line."}, {"keyword": "nextLine", "usage": "nextLine is a method of Scanner class that reads a line of text from the input."}, {"keyword": "parse", "usage": "parse is a method used to convert a string representation to another data type."}, {"keyword": "out", "usage": "out is a static member of the System class that represents the standard output stream."}, {"keyword": "in", "usage": "in is a static member of the System class that represents the standard input stream."}, {"keyword": "Scanner", "usage": "Scanner is a class that provides methods for reading input of various types."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "startDate", "usage": "startDate is a field that stores the start date of the leave."}, {"keyword": "Date", "usage": "Date is a class that represents a specific instant in time, with millisecond precision."}, {"keyword": "SimpleDateFormat", "usage": "SimpleDateFormat is a class that formats and parses dates in a locale-sensitive manner."}, {"keyword": "try", "usage": "try is a keyword used to define a block of code to be tested for errors while it is being executed."}, {"keyword": "catch", "usage": "catch is a keyword used to define a block of code to be executed, if an error occurs in the try block."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}], "color": "#FF6347"}, {"id": "UI5", "sentence_id": 35, "sentence": "System renders End Date as date picker.", "related_lines": [91, 92, 93], "related_keywords": [{"keyword": "System", "usage": "System is a final class in the java.lang package that provides access to system resources."}, {"keyword": "print", "usage": "print is a method of PrintStream class that prints the specified data without terminating the line."}, {"keyword": "nextLine", "usage": "nextLine is a method of Scanner class that reads a line of text from the input."}, {"keyword": "parse", "usage": "parse is a method used to convert a string representation to another data type."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "SimpleDateFormat", "usage": "SimpleDateFormat is a class that formats and parses dates in a locale-sensitive manner."}, {"keyword": "out", "usage": "out is a static member of the System class that represents the standard output stream."}, {"keyword": "in", "usage": "in is a static member of the System class that represents the standard input stream."}, {"keyword": "Scanner", "usage": "Scanner is a class that provides methods for reading input of various types."}, {"keyword": "endDate", "usage": "endDate is a field that stores the end date of the leave."}, {"keyword": "Date", "usage": "Date is a class that represents a specific instant in time, with millisecond precision."}, {"keyword": "try", "usage": "try is a keyword used to define a block of code to be tested for errors while it is being executed."}, {"keyword": "catch", "usage": "catch is a keyword used to define a block of code to be executed, if an error occurs in the try block."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}], "color": "#DA70D6"}, {"id": "UI6", "sentence_id": 36, "sentence": "System renders Number of Days as read-only field.", "related_lines": [76, 106], "related_keywords": [{"keyword": "System", "usage": "System is a final class in the java.lang package that provides access to system resources."}, {"keyword": "println", "usage": "println is a method of PrintStream class that prints the specified data and then terminates the line."}, {"keyword": "+", "usage": "Addition operator that adds two values or concatenates strings."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "getNumberOfDays", "usage": "getNumberOfDays is a method that returns the number of days."}, {"keyword": "out", "usage": "out is a static member of the System class that represents the standard output stream."}, {"keyword": "numberOfDays", "usage": "numberOfDays is a field that stores the number of days for the leave."}, {"keyword": "int", "usage": "int is a primitive data type that stores integer values."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}], "color": "#1E90FF"}, {"id": "UI7", "sentence_id": 37, "sentence": "System renders Leave Type as dropdown.", "related_lines": [94, 95, 96, 97, 98, 99, 100, 101], "related_keywords": [{"keyword": "System", "usage": "System is a final class in the java.lang package that provides access to system resources."}, {"keyword": "println", "usage": "println is a method of PrintStream class that prints the specified data and then terminates the line."}, {"keyword": "int", "usage": "int is a primitive data type that stores integer values."}, {"keyword": "nextInt", "usage": "nextInt is a method of Scanner class that reads an int value from the input."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "[]", "usage": "Square brackets are used for array indexing or declaration."}, {"keyword": "-", "usage": "Subtraction operator that subtracts the right operand from the left operand."}, {"keyword": "values", "usage": "values is a static method that returns an array containing all the enum constants."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "out", "usage": "out is a static member of the System class that represents the standard output stream."}, {"keyword": "in", "usage": "in is a static member of the System class that represents the standard input stream."}, {"keyword": "Scanner", "usage": "Scanner is a class that provides methods for reading input of various types."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "LeaveType", "usage": "LeaveType is an enum type that represents the different types of leave."}, {"keyword": "leaveType", "usage": "leaveType is a field that stores the type of leave."}, {"keyword": "enum", "usage": "enum is a special data type that enables for a variable to be a set of predefined constants."}, {"keyword": "for", "usage": "for is a control flow statement for specifying iteration."}, {"keyword": "length", "usage": "length is a property of arrays that returns the number of elements in the array."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#DB7093"}, {"id": "UI8", "sentence_id": 38, "sentence": "System renders Reason as multi-line text area.", "related_lines": [102, 103], "related_keywords": [{"keyword": "System", "usage": "System is a final class in the java.lang package that provides access to system resources."}, {"keyword": "print", "usage": "print is a method of PrintStream class that prints the specified data without terminating the line."}, {"keyword": "nextLine", "usage": "nextLine is a method of Scanner class that reads a line of text from the input."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "out", "usage": "out is a static member of the System class that represents the standard output stream."}, {"keyword": "in", "usage": "in is a static member of the System class that represents the standard input stream."}, {"keyword": "Scanner", "usage": "Scanner is a class that provides methods for reading input of various types."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "reason", "usage": "reason is a field that stores the reason for the leave."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}], "color": "#F0E68C"}, {"id": "UI9", "sentence_id": 39, "sentence": "System displays Status as hidden field.", "related_lines": [41], "related_keywords": [{"keyword": "this", "usage": "this is a reference to the current object."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "=", "usage": "Assignment operator that assigns a value to a variable."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "ApplicationStatus", "usage": "ApplicationStatus is an enum type that represents the different statuses of a leave application."}, {"keyword": "status", "usage": "status is a field that stores the status of the leave application."}, {"keyword": "PENDING", "usage": "PENDING is a constant in the ApplicationStatus enum representing a pending status."}, {"keyword": "enum", "usage": "enum is a special data type that enables for a variable to be a set of predefined constants."}, {"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "getStatus", "usage": "getStatus is a method that returns the status of the leave application."}], "color": "#3CB371"}, {"id": "UI10", "sentence_id": 40, "sentence": "System displays Available Balance as read-only field.", "related_lines": [107], "related_keywords": [{"keyword": "System", "usage": "System is a final class in the java.lang package that provides access to system resources."}, {"keyword": "println", "usage": "println is a method of PrintStream class that prints the specified data and then terminates the line."}, {"keyword": "+", "usage": "Addition operator that adds two values or concatenates strings."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": "getAvailableBalance", "usage": "getAvailableBalance is a method that returns the available balance."}, {"keyword": "out", "usage": "out is a static member of the System class that represents the standard output stream."}, {"keyword": "availableBalance", "usage": "availableBalance is a field that stores the available leave balance."}, {"keyword": "int", "usage": "int is a primitive data type that stores integer values."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}], "color": "#BC8F8F"}, {"id": "UI11", "sentence_id": 41, "sentence": "System displays error message \"employee id required\" when ID is missing.", "related_lines": [29], "related_keywords": [{"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "ValidationException", "usage": "ValidationException is a custom exception class for validation errors."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "||", "usage": "Logical OR operator that returns true if either operand is true."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "isEmpty", "usage": "isEmpty is a method that checks if a string is empty."}, {"keyword": "employeeId", "usage": "employeeId is a field that stores the employee ID."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": "validate", "usage": "validate is a method that validates the leave application data."}, {"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "void", "usage": "void is a keyword that indicates that a method does not return any value."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#4B0082"}, {"id": "UI12", "sentence_id": 42, "sentence": "System displays error message \"employee name required\" when Name is missing.", "related_lines": [31], "related_keywords": [{"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "ValidationException", "usage": "ValidationException is a custom exception class for validation errors."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "||", "usage": "Logical OR operator that returns true if either operand is true."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "isEmpty", "usage": "isEmpty is a method that checks if a string is empty."}, {"keyword": "employeeName", "usage": "employeeName is a field that stores the employee name."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": "validate", "usage": "validate is a method that validates the leave application data."}, {"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "void", "usage": "void is a keyword that indicates that a method does not return any value."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#556B2F"}, {"id": "UI13", "sentence_id": 43, "sentence": "System displays error message \"start date required\" when Start Date is missing.", "related_lines": [33], "related_keywords": [{"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "ValidationException", "usage": "ValidationException is a custom exception class for validation errors."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "startDate", "usage": "startDate is a field that stores the start date of the leave."}, {"keyword": "Date", "usage": "Date is a class that represents a specific instant in time, with millisecond precision."}, {"keyword": "validate", "usage": "validate is a method that validates the leave application data."}, {"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "void", "usage": "void is a keyword that indicates that a method does not return any value."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#B22222"}, {"id": "UI14", "sentence_id": 44, "sentence": "System displays error message \"end date required\" when End Date is missing.", "related_lines": [35], "related_keywords": [{"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "ValidationException", "usage": "ValidationException is a custom exception class for validation errors."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "endDate", "usage": "endDate is a field that stores the end date of the leave."}, {"keyword": "Date", "usage": "Date is a class that represents a specific instant in time, with millisecond precision."}, {"keyword": "validate", "usage": "validate is a method that validates the leave application data."}, {"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "void", "usage": "void is a keyword that indicates that a method does not return any value."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#228B22"}, {"id": "UI15", "sentence_id": 45, "sentence": "System displays error message \"leave type required\" when Leave Type is missing.", "related_lines": [38], "related_keywords": [{"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "ValidationException", "usage": "ValidationException is a custom exception class for validation errors."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "leaveType", "usage": "leaveType is a field that stores the type of leave."}, {"keyword": "LeaveType", "usage": "LeaveType is an enum type that represents the different types of leave."}, {"keyword": "validate", "usage": "validate is a method that validates the leave application data."}, {"keyword": "private", "usage": "private is an access modifier that restricts access to the class members from outside the class."}, {"keyword": "void", "usage": "void is a keyword that indicates that a method does not return any value."}, {"keyword": "enum", "usage": "enum is a special data type that enables for a variable to be a set of predefined constants."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#5F9EA0"}, {"id": "UI16", "sentence_id": 46, "sentence": "System displays error message \"reason required\" when Reason is missing.", "related_lines": [40], "related_keywords": [{"keyword": "throw", "usage": "throw is used to explicitly throw an exception."}, {"keyword": "new", "usage": "new is used to create an instance of a class or array."}, {"keyword": "ValidationException", "usage": "ValidationException is a custom exception class for validation errors."}, {"keyword": "if", "usage": "if is a conditional statement that executes a block of code if the condition is true."}, {"keyword": "||", "usage": "Logical OR operator that returns true if either operand is true."}, {"keyword": "==", "usage": "Equality operator that checks if two values are equal."}, {"keyword": "null", "usage": "null is a literal that represents a reference that does not refer to any object."}, {"keyword": "isEmpty", "usage": "isEmpty is a method that checks if a string is empty."}, {"keyword": "reason", "usage": "reason is a field that stores the reason for the leave."}, {"keyword": "String", "usage": "String is a class that represents character strings."}, {"keyword": ".", "usage": "Dot operator is used to access members of a class or object."}, {"keyword": ";", "usage": "Semicolon is used to terminate statements in Java."}, {"keyword": "()", "usage": "Parentheses are used to contain method parameters or to control order of operations."}, {"keyword": "{}", "usage": "Curly braces define a code block in Java."}], "color": "#6A5ACD"}]}]}