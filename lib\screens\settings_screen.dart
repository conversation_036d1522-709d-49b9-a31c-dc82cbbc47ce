import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_alert_dialog.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../l10n/app_localizations.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/language_provider.dart';
import '../widgets/navigation_drawer.dart';
import '../theme/app_colors.dart';
import '../theme/spacing.dart';
import '../widgets/responsive_builder.dart';
import '../widgets/common/loading_overlay.dart';
import 'auth/login_screen.dart';
import 'auth/profile_screen.dart';
import '../utils/logger.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String _appVersion = '1.0.0'; // Default version
  bool _isLoggingOut = false; // Manual loading state for logout

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  Future<void> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
      });
      Logger.info('App version: $_appVersion');
    } catch (e) {
      Logger.error('Error getting app version: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get device type for responsive spacing
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);

    return NSLKnowledgeLoaderWrapper(
      isLoading: _isLoggingOut,
      child: SafeArea(
        child: Scaffold(
          drawer: const AppNavigationDrawer(currentRoute: 'settings'),
          appBar: AppBar(
            title: Text(context.tr('settings.title')),
          ),
          body: Stack(
            children: [
              SingleChildScrollView(
                padding: EdgeInsets.all(
                    AppSpacing.getResponsiveSpacing(AppSpacing.md, deviceType)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section title
                    Text(
                      context.tr('settings.appearance'),
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    SizedBox(height: AppSpacing.md),

                    // Dark mode toggle
                    _buildDarkModeToggle(context),

                    SizedBox(height: AppSpacing.md),

                    // Language selection
                    _buildLanguageSelector(context),

                    SizedBox(height: AppSpacing.xl),

                    // About section
                    Text(
                      context.tr('settings.about'),
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    SizedBox(height: AppSpacing.md),

                    // App info
                    _buildInfoCard(
                      context,
                      title: context.tr('settings.version'),
                      description: _appVersion,
                      icon: Icons.info_outline,
                    ),

                    SizedBox(height: AppSpacing.xl),

                    // Account section
                    Text(
                      context.tr('settings.account'),
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    SizedBox(height: AppSpacing.md),

                    // Profile button
                    _buildProfileButton(context),

                    SizedBox(height: AppSpacing.md),

                    // Logout button
                    _buildLogoutButton(context),
                  ],
                ),
              ),

              // Full-screen loading overlay for logout
              // if (_isLoggingOut)
              //   LoadingOverlay(
              //     text: context.tr('auth.loggingOut'),
              //     color: Theme.of(context).colorScheme.error,
              //     indicatorSize: 30,
              //     fontSize: 16,
              //   ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDarkModeToggle(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, _) {
        return Card(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Row(
              children: [
                const Icon(Icons.dark_mode_outlined),
                SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.tr('settings.darkMode'),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Text(
                        context.tr('settings.darkModeDescription'),
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: themeProvider.themeMode == ThemeMode.dark,
                  activeColor: AppColors.primaryIndigo,
                  onChanged: (value) {
                    themeProvider.setThemeMode(
                      value ? ThemeMode.dark : ThemeMode.light,
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
  }) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Row(
          children: [
            Icon(icon),
            SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to determine device type based on screen width
  DeviceType getDeviceType(double width) {
    if (width < 600) {
      return DeviceType.mobile;
    } else if (width < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  // Build profile button
  Widget _buildProfileButton(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        final user = authProvider.user;
        return Card(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ProfileScreen()),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.md),
              child: Row(
                children: [
                  CircleAvatar(
                    backgroundColor:
                        Theme.of(context).colorScheme.primary.withAlpha(50),
                    child: Icon(
                      Icons.person,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.tr('profile.title'),
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        Text(
                          user != null
                              ? user.email
                              : context.tr('profile.viewProfileDetails'),
                          style: Theme.of(context).textTheme.bodySmall,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color:
                        Theme.of(context).colorScheme.onSurface.withAlpha(128),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Build language selector
  Widget _buildLanguageSelector(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, _) {
        return Card(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.language),
                    SizedBox(width: AppSpacing.md),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context.tr('settings.language'),
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          Text(
                            context.tr('settings.languageDescription'),
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppSpacing.md),
                Wrap(
                  spacing: AppSpacing.sm,
                  children: [
                    _buildLanguageOption(
                      context,
                      'English',
                      const Locale('en', 'US'),
                      languageProvider,
                    ),
                    _buildLanguageOption(
                      context,
                      'తెలుగు', // Telugu
                      const Locale('te', 'IN'),
                      languageProvider,
                    ),
                    _buildLanguageOption(
                      context,
                      'हिन्दी', // Hindi
                      const Locale('hi', 'IN'),
                      languageProvider,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Build language option
  Widget _buildLanguageOption(
    BuildContext context,
    String label,
    Locale locale,
    LanguageProvider languageProvider,
  ) {
    final isSelected =
        languageProvider.locale.languageCode == locale.languageCode;

    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          languageProvider.setLocale(locale);
        }
      },
      backgroundColor: Theme.of(context).colorScheme.surface,
      selectedColor: Theme.of(context).colorScheme.primary.withAlpha(50),
      labelStyle: TextStyle(
        color: isSelected
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurface,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  // Build logout button
  Widget _buildLogoutButton(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        return Card(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: () {
              showDialog(
                context: context,
                builder: (context) => CustomAlertDialog(
                  title: 'navigation.logout',
                  content: 'navigation.logoutConfirmation',
                  onClose: () => Navigator.of(context).pop(),
                  primaryButtonText: 'common.cancel',
                  onPrimaryPressed: () {
                    // Handle primary action
                    Navigator.of(context).pop();
                  },
                  secondaryButtonText: 'navigation.logout',
                  onSecondaryPressed: () async {
                        Navigator.pop(context); // Close dialog

                        // Set loading state to true
                        setState(() {
                          _isLoggingOut = true;
                        });

                        try {
                          // Logout user
                          final success = await authProvider.logout();

                          // Navigate to login screen
                          if (mounted) {
                            if (success) {
                              Navigator.of(context).pushAndRemoveUntil(
                                MaterialPageRoute(
                                    builder: (context) => const LoginScreen()),
                                (route) => false,
                              );
                            } else {
                              // Show error message if logout failed
                              final scaffoldMessenger =
                                  ScaffoldMessenger.of(context);
                              final errorColor =
                                  Theme.of(context).colorScheme.error;

                              scaffoldMessenger.showSnackBar(
                                SnackBar(
                                  content: Text(
                                      authProvider.error ?? 'Logout failed'),
                                  backgroundColor: errorColor,
                                ),
                              );
                            }
                          }
                        } catch (e) {
                          Logger.error('Error during logout: $e');

                          if (mounted) {
                            // Show error message
                            final scaffoldMessenger =
                                ScaffoldMessenger.of(context);
                            final errorColor =
                                Theme.of(context).colorScheme.error;

                            scaffoldMessenger.showSnackBar(
                              SnackBar(
                                content: Text('Logout failed: ${e.toString()}'),
                                backgroundColor: errorColor,
                              ),
                            );
                          }
                        } finally {
                          // Reset loading state
                          if (mounted) {
                            setState(() {
                              _isLoggingOut = false;
                            });
                          }
                        }
                      },
                     
                ),
              );

              // Show confirmation dialog
            //   showDialog(
            //     context: context,
            //     builder: (context) => AlertDialog(
            //       title: Text(context.tr('navigation.logout')),
            //       content: Text(context.tr('navigation.logoutConfirmation')),
            //       actions: [
            //         TextButton(
            //           onPressed: () => Navigator.pop(context),
            //           child: Text(context.tr('common.cancel')),
            //         ),
            //         TextButton(
            //           onPressed: () async {
            //             Navigator.pop(context); // Close dialog

            //             // Set loading state to true
            //             setState(() {
            //               _isLoggingOut = true;
            //             });

            //             try {
            //               // Logout user
            //               final success = await authProvider.logout();

            //               // Navigate to login screen
            //               if (mounted) {
            //                 if (success) {
            //                   Navigator.of(context).pushAndRemoveUntil(
            //                     MaterialPageRoute(
            //                         builder: (context) => const LoginScreen()),
            //                     (route) => false,
            //                   );
            //                 } else {
            //                   // Show error message if logout failed
            //                   final scaffoldMessenger =
            //                       ScaffoldMessenger.of(context);
            //                   final errorColor =
            //                       Theme.of(context).colorScheme.error;

            //                   scaffoldMessenger.showSnackBar(
            //                     SnackBar(
            //                       content: Text(
            //                           authProvider.error ?? 'Logout failed'),
            //                       backgroundColor: errorColor,
            //                     ),
            //                   );
            //                 }
            //               }
            //             } catch (e) {
            //               Logger.error('Error during logout: $e');

            //               if (mounted) {
            //                 // Show error message
            //                 final scaffoldMessenger =
            //                     ScaffoldMessenger.of(context);
            //                 final errorColor =
            //                     Theme.of(context).colorScheme.error;

            //                 scaffoldMessenger.showSnackBar(
            //                   SnackBar(
            //                     content: Text('Logout failed: ${e.toString()}'),
            //                     backgroundColor: errorColor,
            //                   ),
            //                 );
            //               }
            //             } finally {
            //               // Reset loading state
            //               if (mounted) {
            //                 setState(() {
            //                   _isLoggingOut = false;
            //                 });
            //               }
            //             }
            //           },
            //           style: TextButton.styleFrom(
            //             foregroundColor: Theme.of(context).colorScheme.error,
            //           ),
            //           child: Text(context.tr('navigation.logout')),
            //         ),
            //       ],
            //     ),
            //   );
            },
            child: Padding(
              padding: const EdgeInsets.all(AppSpacing.md),
              child: Row(
                children: [
                  Icon(
                    Icons.logout,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.tr('navigation.logout'),
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Theme.of(context).colorScheme.error,
                                  ),
                        ),
                        Text(
                          context.tr('auth.signOutOfAccount'),
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color:
                        Theme.of(context).colorScheme.onSurface.withAlpha(128),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
