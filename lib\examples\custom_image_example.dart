import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/custom_image.dart';

/// Example widget that demonstrates how to use the CustomImage class
class CustomImageExample extends StatefulWidget {
  const CustomImageExample({super.key});

  @override
  State<CustomImageExample> createState() => _CustomImageExampleState();
}

class _CustomImageExampleState extends State<CustomImageExample> {
  List<CustomImage> _images = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadImages();
  }

  /// Load images from JSON file
  Future<void> _loadImages() async {
    try {
      // Load from JSON file
      final String jsonString = await rootBundle.loadString('assets/data/images_sample.json');
      final List<dynamic> jsonData = json.decode(jsonString) as List<dynamic>;
      
      setState(() {
        _images = jsonData
            .map((item) => CustomImage.fromJson(item as Map<String, dynamic>))
            .toList();
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading images: $e');
      
      // Fallback data if JSON loading fails
      setState(() {
        _images = [
          CustomImage.asset(
            'assets/images/collections_1.png',
            width: 100,
            height: 100,
            fit: BoxFit.cover,
          ),
          CustomImage.network(
            'https://example.com/image.jpg',
            width: 200,
            height: 150,
            fit: BoxFit.contain,
            placeholder: 'assets/images/placeholder.png',
          ),
        ];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CustomImage Example'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Images from JSON:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  
                  // Display all images loaded from JSON
                  ..._images.map((image) => Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Type: ${image.type.toString().split('.').last}'),
                        Text('Path: ${image.path}'),
                        const SizedBox(height: 8),
                        image.toWidget(),
                      ],
                    ),
                  )),
                  
                  const Divider(height: 32),
                  
                  // Examples of creating CustomImage instances programmatically
                  const Text(
                    'Programmatic Examples:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  
                  // Asset image example
                  const Text('Asset Image:'),
                  const SizedBox(height: 8),
                  CustomImage.asset(
                    'assets/images/collections_1.png',
                    width: 150,
                    height: 100,
                    fit: BoxFit.cover,
                  ).toWidget(),
                  
                  const SizedBox(height: 16),
                  
                  // Network image example
                  const Text('Network Image with Custom Error Handler:'),
                  const SizedBox(height: 8),
                  CustomImage.network(
                    'https://example.com/nonexistent.jpg',
                    width: 150,
                    height: 100,
                  ).toWidget(
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 150,
                        height: 100,
                        color: Colors.grey[300],
                        child: const Center(
                          child: Icon(Icons.error, color: Colors.red),
                        ),
                      );
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // JSON serialization example
                  const Text('JSON Serialization Example:'),
                  const SizedBox(height: 8),
                  _buildJsonExample(),
                ],
              ),
            ),
    );
  }

  Widget _buildJsonExample() {
    // Create a custom image
    final customImage = CustomImage.asset(
      'assets/images/icons/procurement_icon.png',
      width: 32,
      height: 32,
      color: Colors.blue,
    );
    
    // Convert to JSON
    final jsonString = customImage.toJsonString();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('JSON: $jsonString'),
        const SizedBox(height: 8),
        const Text('Deserialized Image:'),
        const SizedBox(height: 4),
        CustomImage.fromJsonString(jsonString).toWidget(),
      ],
    );
  }
}
