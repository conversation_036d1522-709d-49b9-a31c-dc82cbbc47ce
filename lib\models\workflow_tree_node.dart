class WorkflowTreeNode {
  final String id;
  final String text;
  final String? altText;
  final int level;
  final int sequence;
  final WorkflowNodeType nodeType;
  final String? actorType;
  final bool isExpanded;
  final bool isTerminal;
  final bool isParallel;
  final bool hasCheckmark;
  final bool hasX;
  final List<WorkflowTreeNode> children;
  final Map<String, dynamic>? metadata;

  WorkflowTreeNode({
    required this.id,
    required this.text,
    this.altText,
    required this.level,
    required this.sequence,
    required this.nodeType,
    this.actorType,
    this.isExpanded = false,
    this.isTerminal = false,
    this.isParallel = false,
    this.hasCheckmark = false,
    this.hasX = false,
    this.children = const [],
    this.metadata,
  });

  WorkflowTreeNode copyWith({
    String? id,
    String? text,
    String? altText,
    int? level,
    int? sequence,
    WorkflowNodeType? nodeType,
    String? actorType,
    bool? isExpanded,
    bool? isTerminal,
    bool? isParallel,
    bool? hasCheckmark,
    bool? hasX,
    List<WorkflowTreeNode>? children,
    Map<String, dynamic>? metadata,
  }) {
    return WorkflowTreeNode(
      id: id ?? this.id,
      text: text ?? this.text,
      altText: altText ?? this.altText,
      level: level ?? this.level,
      sequence: sequence ?? this.sequence,
      nodeType: nodeType ?? this.nodeType,
      actorType: actorType ?? this.actorType,
      isExpanded: isExpanded ?? this.isExpanded,
      isTerminal: isTerminal ?? this.isTerminal,
      isParallel: isParallel ?? this.isParallel,
      hasCheckmark: hasCheckmark ?? this.hasCheckmark,
      hasX: hasX ?? this.hasX,
      children: children ?? this.children,
      metadata: metadata ?? this.metadata,
    );
  }
}

enum WorkflowNodeType {
  globalObjective,
  pathway,
  localObjective,
  businessRule,
  parallel,
  condition,
  terminal,
}
