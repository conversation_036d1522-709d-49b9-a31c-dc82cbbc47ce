import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart' as f1;
import 'package:pie_chart/pie_chart.dart' as pc;
import 'package:flutter_svg/flutter_svg.dart';

class TransactionsPage extends StatelessWidget {
  const TransactionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    List<String> taskPriorityTwo = [
      "Task priority in next 2 Days",
      "User Registration",
      "Submit Form",
      "Approve Application",
      "Payment Gateway Page"
    ];

    List<String> taskPrioritySeven = [
      "Task priority in next 7 Days",
      "User Profile",
      "Forgot Password",
      "Payment Gateway",
      "Approve Application"
    ];

    return Scaffold(
      backgroundColor: const Color.fromRGBO(237, 237, 237, 1),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Top row: title & search
            LayoutBuilder(
              builder: (context, constraints) {
                bool isSmallScreen = constraints.maxWidth < 400;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    isSmallScreen
                        ? Column(
                            // Stack vertically on small screens
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Positioned(
                                top: 29,
                                left: 90,
                                child: SizedBox(
                                  width: 160,
                                  height: 24,
                                  child: Text(
                                    "My Transaction",
                                    textAlign: TextAlign.left,
                                    style: TextStyle(
                                      fontSize: 20,
                                      height:
                                          1.2, // 24px line height ÷ 20px font size = 1.2
                                      fontWeight: FontWeight.bold,
                                      fontFamily:
                                          'Inter', // Make sure you have added 'Inter' font in pubspec.yaml
                                      letterSpacing: 0,
                                      color: Color(0xFF606060),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 10),
                              Positioned(
                                top: 22,
                                left: 1577, // Only if inside a Stack!
                                child: SizedBox(
                                  width: 324,
                                  height: 36,
                                  child: TextField(
                                    decoration: InputDecoration(
                                      hintText: "Search Transaction",
                                      suffixIcon: Icon(Icons.search),
                                      filled: true,
                                      fillColor:
                                          Colors.white, // background: #FFFFFF
                                      contentPadding:
                                          EdgeInsets.symmetric(horizontal: 10),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(
                                            6), // border-radius: 6px
                                        borderSide: BorderSide(
                                          color:
                                              Color(0xFFCCCCCC), // border color
                                          width: 1,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: Color(0xFFCCCCCC),
                                          width: 1,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          )
                        : Row(
                            // Normal row layout for medium/large screens
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Positioned(
                                top: 29,
                                left: 90,
                                child: SizedBox(
                                  width: 160,
                                  height: 24,
                                  child: Text(
                                    "My Transaction",
                                    textAlign: TextAlign.left,
                                    style: TextStyle(
                                      fontSize: 20,
                                      height:
                                          1.2, // 24px line height ÷ 20px font size = 1.2
                                      fontWeight: FontWeight.bold,
                                      fontFamily:
                                          'Inter', // Make sure you have added 'Inter' font in pubspec.yaml
                                      letterSpacing: 0,
                                      color: Color(0xFF606060),
                                    ),
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 22,
                                left: 1577, // Only if inside a Stack!
                                child: SizedBox(
                                  width: 324,
                                  height: 36,
                                  child: TextField(
                                    decoration: InputDecoration(
                                      hintText: "Search Transaction",
                                      suffixIcon: Icon(Icons.search),
                                      filled: true,
                                      fillColor:
                                          Colors.white, // background: #FFFFFF
                                      contentPadding:
                                          EdgeInsets.symmetric(horizontal: 10),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(
                                            6), // border-radius: 6px
                                        borderSide: BorderSide(
                                          color:
                                              Color(0xFFCCCCCC), // border color
                                          width: 1,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(6),
                                        borderSide: BorderSide(
                                          color: Color(0xFFCCCCCC),
                                          width: 1,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                    const SizedBox(height: 20),
                  ],
                );
              },
            ),

            //  const SizedBox(height: 20),

            // Four static blocks in one row
            Wrap(
              spacing: 12, // space between items horizontally
              runSpacing: 12, // space between items vertically
              children: [
                _buildBox(taskPriorityTwo),
                _buildBox(taskPrioritySeven),
                _buildBoxPieChart("Status of all Solutions", "Total 100"),
                _buildBoxLineChart("Work-in-progress Tasks Timeline"),
              ],
            ),

            // Just below your Row of 4 blocks:
            const SizedBox(height: 20),
            Expanded(child: TabChartSection()),
          ],
        ),
      ),
    );
  }
}

Widget _buildBox(List taskPriority) {
  return LayoutBuilder(
    builder: (context, constraints) {
      double screenWidth = MediaQuery.of(context).size.width;
      double boxWidth;

      if (screenWidth >= 1000) {
        boxWidth = screenWidth / 5;
      } else if (screenWidth >= 600) {
        boxWidth = screenWidth / 2.5;
      } else {
        boxWidth = screenWidth - 40;
      }

      // Split title into left and right parts
      List<String> parts = taskPriority[0].split(" ");
      String leftTitle = parts.sublist(0, parts.length - 2).join(" ");
      String rightTitle = parts.sublist(parts.length - 2).join(" ");
      // String daysColor = "";
      Color daysColor; // Declare it first
      Color circleColor; // Declare it first

      if (rightTitle == "2 Days") {
        daysColor = const Color(0xFFF37C74); // Or assigned based on a condition
        circleColor = const Color(0xFFFF0000);
      } else {
        daysColor = const Color(0xFFF5BA76); // Or assigned based on a condition
        circleColor = const Color(0xFFFECA8E);
      }
      return SizedBox(
        width: boxWidth,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white, // White block background
            // border: Border.all(color: Colors.orange),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with dynamic title split
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    leftTitle,
                    textAlign: TextAlign.left, // CSS: text-align: left
                    style: const TextStyle(
                      //  fontSize: 14, // CSS: font-size: 18px
                      fontWeight: FontWeight.w600, // CSS: font-weight: 600
                      height: 16 /
                          18, // CSS: line-height: 16px -> Flutter uses a multiplier of font size
                      letterSpacing: 0.0, // CSS: letter-spacing: 0px
                      color: Color(0xFF606060), // CSS: color: #606060
                    ),
                  ),
                  Text(
                    rightTitle,
                    style: TextStyle(
                      // fontSize: 16, // CSS: font-size: 26px
                      color: daysColor, // dynamic color
                      fontWeight: FontWeight
                          .normal, // CSS didn't specify bold, so use normal
                    ),
                  ),
                ],
              ),

              const Divider(),

              // Dynamic content
              Column(
                children: taskPriority.sublist(1).map((content) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            // Red circle icon with arrow
                            Container(
                              width: 16,
                              height: 16,
                              decoration: BoxDecoration(
                                color: circleColor,
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Transform.rotate(
                                  angle: -0.785398, // -45 degrees
                                  child: const Icon(
                                    Icons.arrow_forward,
                                    size: 12,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              content,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                        const Text(">", style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 12),

              // Pagination control with dots
              Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildLeftCircle(Icons.arrow_back),
                    const SizedBox(width: 8),
                    const Text("...", style: TextStyle(fontSize: 16)),
                    const SizedBox(width: 8),
                    _buildRightCircle(Icons.arrow_forward),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}

Widget _buildLeftCircle(IconData iconData) {
  return Opacity(
    opacity: 0.6, // Fully visible
    child: _buildCircleArrow(iconData),
  );
}

Widget _buildRightCircle(IconData iconData) {
  return Opacity(
    opacity: 1.0, // 60% opacity
    child: _buildCircleArrow(iconData),
  );
}

// Reusable helper for circular arrow button
Widget _buildCircleArrow(IconData iconData) {
  return Container(
    width: 20, // Keep your circle size
    height: 20,
    decoration: BoxDecoration(
      shape: BoxShape.circle,
      border: Border.all(color: Colors.black, width: 1), // Updated border
    ),
    child: Center(
      child: Icon(
        iconData,
        size: 16,
        color: Colors.black,
      ),
    ),
  );
}

Widget _buildBoxPieChart(String heading, String content) {
  final double completed = 56;
  final double pending = 44;

  final Color completedColor =
      const Color.fromRGBO(145, 209, 131, 1); // Completed
  final Color pendingColor = const Color.fromRGBO(255, 229, 137, 1); // Pending

  return LayoutBuilder(
    builder: (context, constraints) {
      double screenWidth = MediaQuery.of(context).size.width;
      double boxWidth;

      if (screenWidth >= 1000) {
        boxWidth = screenWidth / 5;
      } else if (screenWidth >= 600) {
        boxWidth = screenWidth / 2.5;
      } else {
        boxWidth = screenWidth - 40;
      }

      return SizedBox(
        width: boxWidth,
        child: Container(
          height: 210,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            children: [
              Text(
                heading,
                textAlign: TextAlign.left,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  height: 1.0,
                  letterSpacing: 0.0,
                  color: Color(0xFF606060),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 120,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    f1.PieChart(
                      f1.PieChartData(
                        sectionsSpace: 0,
                        centerSpaceRadius: 35,
                        sections: [
                          f1.PieChartSectionData(
                            value: completed,
                            title: completed.toInt().toString(),
                            color: completedColor,
                            radius: 25,
                            titleStyle: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                            titlePositionPercentageOffset: 0.6,
                          ),
                          f1.PieChartSectionData(
                            value: pending,
                            title: pending.toInt().toString(),
                            color: pendingColor,
                            radius: 25,
                            titleStyle: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                            titlePositionPercentageOffset: 0.6,
                          ),
                        ],
                      ),
                    ),
                    Text(
                      content,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildLegend(color: pendingColor, label: "Pending"),
                  const SizedBox(width: 20),
                  _buildLegend(color: completedColor, label: "Completed"),
                ],
              ),
            ],
          ),
        ),
      );
    },
  );
}

Widget _buildBoxLineChart(String heading) {
  return LayoutBuilder(
    builder: (context, constraints) {
      double screenWidth = MediaQuery.of(context).size.width;
      double boxWidth;
      double chartHeight;

      if (screenWidth >= 1000) {
        boxWidth = screenWidth / 3.2;
        chartHeight = 157; // increase from 150
      } else if (screenWidth >= 600) {
        boxWidth = screenWidth / 2.2;
        chartHeight = 152; // increse from 140
      } else {
        boxWidth = screenWidth - 40;
        chartHeight = 130; // reduced from 160
      }

      return SizedBox(
        width: boxWidth,
        child: Container(
          padding: const EdgeInsets.all(12), // reduced padding
          decoration: BoxDecoration(
            color: Colors.white, // White block background
            //     border: Border.all(color: Colors.orange),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and Filter Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    heading,
                    textAlign: TextAlign.left, // CSS: text-align: left
                    style: const TextStyle(
                      //  fontSize: 12, // CSS: font-size: 12px
                      height: 15 /
                          12, // CSS: line-height: 15px → Flutter: height = line-height / font-size
                      fontWeight: FontWeight.bold, // CSS: font-weight: bold
                      letterSpacing: 0.0, // CSS: letter-spacing: 0px
                      color: Color(0xFF606060), // CSS: color: #606060
                      fontFamily: 'Inter', // CSS: font-family: Inter
                    ),
                  ),
                  const Row(
                    children: [
                      Text("5D | ", style: TextStyle(fontSize: 12)),
                      Text("1M | ", style: TextStyle(fontSize: 12)),
                      Text("3M | ", style: TextStyle(fontSize: 12)),
                      Text("6M | ", style: TextStyle(fontSize: 12)),
                      Text(
                        "1Y",
                        style: TextStyle(
                          fontSize: 12,
                          decoration: TextDecoration.underline,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  )
                ],
              ),
              const SizedBox(height: 6), // reduced spacing

              SizedBox(
                height: chartHeight,
                width: double.infinity,
                child: f1.LineChart(
                  f1.LineChartData(
                    titlesData: f1.FlTitlesData(
                      show: true,
                      topTitles: f1.AxisTitles(
                        sideTitles: f1.SideTitles(showTitles: false),
                      ),
                      rightTitles: f1.AxisTitles(
                        sideTitles: f1.SideTitles(showTitles: false),
                      ),
                      bottomTitles: f1.AxisTitles(
                        sideTitles: f1.SideTitles(
                          showTitles: true,
                          interval: 1,
                          getTitlesWidget: (value, meta) {
                            const months = [
                              'Apr',
                              'May',
                              'Jun',
                              'Jul',
                              'Aug',
                              'Sep',
                              'Oct',
                              'Nov',
                              'Dec',
                              'Jan',
                              'Feb',
                              'Mar'
                            ];
                            if (value.toInt() >= 0 &&
                                value.toInt() < months.length) {
                              return Text(
                                months[value.toInt()],
                                style: const TextStyle(fontSize: 10),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                      ),
                      leftTitles: f1.AxisTitles(
                        sideTitles: f1.SideTitles(
                          showTitles: true,
                          reservedSize: 30,
                          interval: 45,
                          getTitlesWidget: (value, meta) {
                            return Text(
                              '${value.toInt()}',
                              style: const TextStyle(fontSize: 10),
                            );
                          },
                        ),
                      ),
                    ),
                    gridData: f1.FlGridData(show: true),
                    borderData: f1.FlBorderData(show: true),
                    minX: 0,
                    maxX: 11,
                    minY: 0,
                    maxY: 180,
                    lineBarsData: [
                      f1.LineChartBarData(
                        isCurved: true,
                        curveSmoothness: 0.6, // Smoother curve
                        spots: [
                          f1.FlSpot(0, 45),
                          f1.FlSpot(2, 55),
                          f1.FlSpot(4, 53),
                          f1.FlSpot(6, 50),
                          f1.FlSpot(8, 47),
                          f1.FlSpot(11, 45),
                        ],
                        barWidth: 3,
                        color: const Color(0xFF66BA04), // Green
                        dotData: f1.FlDotData(show: true),
                      ),
                      f1.LineChartBarData(
                        isCurved: true,
                        curveSmoothness: 0.6,
                        spots: [
                          f1.FlSpot(0, 90),
                          f1.FlSpot(2, 97),
                          f1.FlSpot(4, 100),
                          f1.FlSpot(6, 98),
                          f1.FlSpot(9, 93),
                          f1.FlSpot(11, 90),
                        ],
                        barWidth: 3,
                        color: const Color(0xFF00008B), // Dark Blue
                        dotData: f1.FlDotData(show: true),
                      ),
                      f1.LineChartBarData(
                        isCurved: true,
                        curveSmoothness: 0.6,
                        spots: [
                          f1.FlSpot(0, 135),
                          f1.FlSpot(2, 138),
                          f1.FlSpot(5, 140),
                          f1.FlSpot(7, 137),
                          f1.FlSpot(9, 135),
                          f1.FlSpot(11, 133),
                        ],
                        barWidth: 3,
                        color: const Color(0xFFD862FC), // Purple Pink
                        dotData: f1.FlDotData(show: true),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}

Widget _buildLegend({required Color color, required String label}) {
  return Row(
    children: [
      Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color,
        ),
      ),
      const SizedBox(width: 4),
      Text(
        label,
        style: const TextStyle(fontSize: 12, color: Colors.black),
      ),
    ],
  );
}

class TabChartSection extends StatefulWidget {
  const TabChartSection({super.key});

  @override
  State<TabChartSection> createState() => _TabChartSectionState();
}

class _TabChartSectionState extends State<TabChartSection> {
  String selectedTab = 'All';

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        color: const Color.fromRGBO(245, 245, 245, 1),
        child: Center(
          child: ConstrainedBox(
            constraints:
                const BoxConstraints(maxWidth: 1300), // ⬅️ Reduced width
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Tabs and filter row
                LayoutBuilder(
                  builder: (context, constraints) {
                    bool isSmallScreen = constraints.maxWidth < 500;

                    if (isSmallScreen) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            spacing: 10,
                            runSpacing: 10,
                            children: [
                              _buildTab("All"),
                              _buildTab("Pending"),
                              _buildTab("Completed"),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: const [
                                  Icon(Icons.filter_alt_outlined,
                                      color: Colors.grey),
                                  SizedBox(width: 5),
                                  Text("Filter",
                                      style: TextStyle(color: Colors.grey)),
                                ],
                              ),
                              const Text("< 1 | 2 | 3 >",
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold)),
                            ],
                          ),
                        ],
                      );
                    } else {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              _buildTab("All"),
                              const SizedBox(width: 10),
                              _buildTab("Pending"),
                              const SizedBox(width: 10),
                              _buildTab("Completed"),
                            ],
                          ),
                          Row(
                            children: [
                              const Icon(Icons.filter_alt_outlined,
                                  color: Colors.grey),
                              const SizedBox(width: 5),
                              const Text("Filter",
                                  style: TextStyle(color: Colors.grey)),
                              const SizedBox(width: 20),
                              const Text("< 1 | 2 | 3 >",
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold)),
                            ],
                          ),
                        ],
                      );
                    }
                  },
                ),
                const SizedBox(height: 10),
                _buildChartContent(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTab(String label) {
    final bool isSelected = selectedTab == label;
    return InkWell(
      onTap: () => setState(() => selectedTab = label),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.black : Colors.grey,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 12,
            decoration:
                isSelected ? TextDecoration.underline : TextDecoration.none,
          ),
        ),
      ),
    );
  }

  Widget _buildChartContent() {
    ScrollController _scrollController = ScrollController();

    return Expanded(
      child: Scrollbar(
        thumbVisibility: true,
        controller: _scrollController,
        child: SingleChildScrollView(
          controller: _scrollController,
          child: LayoutBuilder(
            builder: (context, constraints) {
              double screenWidth = constraints.maxWidth;
              int crossAxisCount = screenWidth ~/ 300; // adjust block width
              double blockWidth =
                  (screenWidth - ((crossAxisCount - 1) * 10)) / crossAxisCount;

              return Wrap(
                spacing: 10,
                runSpacing: 10,
                children: List.generate(12, (index) {
                  return SizedBox(
                    width: blockWidth,
                    child: _workflowBlock(),
                  );
                }),
              );
            },
          ),
        ),
      ),
    );
  }
}

Widget _workflowBlock() {
  return SizedBox(
    width: 292,
    height: 133,
    child: Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A000000),
            offset: Offset(0, 2),
            blurRadius: 0,
          ),
        ],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Title Row
          Row(
            children: [
              Container(
                width: 18,
                height: 18,
                padding:
                    const EdgeInsets.all(2), // optional: add spacing inside
                child: SvgPicture.asset(
                  'assets/images/usermanagement.svg',
                  color: Colors.grey, // optional: if your SVG supports it
                ),
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  "User Management Workflow",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                ),
              ),
            ],
          ),

          const Spacer(), // Push the bottom section down

          /// "Assigned to me" near horizontal line
          Align(
            alignment: Alignment.centerRight,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  'assets/images/assignedtome.svg', // or 'assets/icons/assigned_to_me.svg'
                  width: 8,
                  height: 8,
                  color: Colors.black54, // Optional: apply color tint
                ),
                const SizedBox(width: 4),
                const Text(
                  "Assigned to me",
                  style: TextStyle(fontSize: 12, color: Colors.black54),
                ),
              ],
            ),
          ),

          //const SizedBox(height: 4), // Small gap before line

          const Divider(thickness: 1, color: Colors.grey),

          /// Bottom Row with Date + Status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              /// Updated On
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: const [
                  Text(
                    "Updated on:",
                    style: TextStyle(fontSize: 10, color: Colors.black45),
                  ),
                  SizedBox(height: 2),
                  Text(
                    "13 May 2025",
                    style: TextStyle(fontSize: 10, color: Colors.black45),
                  ),
                ],
              ),

              /// Status Tags
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Color(0xFFE4E4E4), width: 1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    alignment: Alignment.center,
                    child: const Text(
                      "1Lo",
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Container(
                    width: 64,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Color(0xFFFEF7E2),
                      borderRadius: BorderRadius.circular(13),
                    ),
                    alignment: Alignment.center,
                    child: const Text(
                      "Pending",
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    ),
  );
}
