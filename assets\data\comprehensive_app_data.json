{"appMetrics": {"booksCount": 12, "solutionsCount": 35, "objectsCount": 102}, "navigation": {"items": [{"id": "1", "icon": "chat_bubble_outline", "route": "/chat", "isSelected": false}, {"id": "2", "icon": "apps", "route": "/apps", "isSelected": true}, {"id": "3", "icon": "work_outline", "route": "/work", "isSelected": false}, {"id": "4", "icon": "description_outlined", "route": "/documents", "isSelected": false}, {"id": "5", "icon": "calendar_today", "route": "/calendar", "isSelected": false}, {"id": "6", "icon": "notifications_none", "route": "/notifications", "isSelected": false}, {"id": "7", "icon": "person_outline", "route": "/profile", "isSelected": false}]}, "books": {"pageTitle": "My Books", "createButtonText": "Create Book", "items": [{"id": "1", "title": "Ecommerce", "categoryType": "B2C", "imageUrl": "https://placehold.co/400x300/3498db/FFFFFF/png?text=Ecommerce", "isDraft": false}, {"id": "2", "title": "Fashion & Apparel", "categoryType": "B2C", "imageUrl": "https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Fashion", "isDraft": false}, {"id": "3", "title": "Financial Advisory", "categoryType": "B2C", "imageUrl": "https://placehold.co/400x300/3498db/FFFFFF/png?text=Finance", "isDraft": false}, {"id": "4", "title": "Home Rentals", "categoryType": "B2C", "imageUrl": "https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Rentals", "isDraft": true}, {"id": "5", "title": "Online Grocery", "categoryType": "B2C", "imageUrl": "https://placehold.co/400x300/3498db/FFFFFF/png?text=Grocery", "isDraft": false}, {"id": "6", "title": "Courier & Logistics", "categoryType": "B2C", "imageUrl": "https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Logistics", "isDraft": false}, {"id": "7", "title": "Automotive", "categoryType": "B2C", "imageUrl": "https://placehold.co/400x300/3498db/FFFFFF/png?text=Automotive", "isDraft": true}, {"id": "8", "title": "Fitness & Wellness", "categoryType": "B2C", "imageUrl": "https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Fitness", "isDraft": false}, {"id": "9", "title": "Real Estate", "categoryType": "B2C", "imageUrl": "https://placehold.co/400x300/3498db/FFFFFF/png?text=Real+Estate", "isDraft": false}, {"id": "10", "title": "Restaurant & Cafe", "categoryType": "B2C", "imageUrl": "https://placehold.co/400x300/95a5a6/FFFFFF/png?text=Restaurant", "isDraft": false}]}, "solutions": {"pageTitle": "My Solutions", "createButtonText": "Create Solution", "items": [{"id": "1", "title": "Ecommerce", "categoryType": "B2C", "version": "V00172"}, {"id": "2", "title": "Ecommerce", "categoryType": "B2C", "version": "V00172"}, {"id": "3", "title": "Ecommerce", "categoryType": "B2C", "version": "V00172"}, {"id": "4", "title": "Ecommerce", "categoryType": "B2C", "version": "V00172"}, {"id": "5", "title": "Ecommerce", "categoryType": "B2C", "version": "V00172"}, {"id": "6", "title": "Ecommerce", "categoryType": "B2C", "version": "V00172"}, {"id": "7", "title": "Ecommerce", "categoryType": "B2C", "version": "V00172"}, {"id": "8", "title": "Ecommerce", "categoryType": "B2C", "version": "V00172"}, {"id": "9", "title": "Ecommerce", "categoryType": "B2C", "version": "V00172"}, {"id": "10", "title": "Ecommerce", "categoryType": "B2C", "version": "V00172"}]}, "objects": {"pageTitle": "My Objects", "createButtonText": "Create Object", "items": [{"id": "1", "title": "Customer", "version": "V00172", "icon": "person"}, {"id": "2", "title": "Product", "version": "V00172", "icon": "shopping_bag"}, {"id": "3", "title": "Address", "version": "V00172", "icon": "location_on"}, {"id": "4", "title": "Employee", "version": "V00172", "icon": "badge"}, {"id": "5", "title": "New Launch", "version": "V00172", "icon": "rocket_launch"}, {"id": "6", "title": "Customer", "version": "V00172", "icon": "person"}, {"id": "7", "title": "Product", "version": "V00172", "icon": "shopping_bag"}, {"id": "8", "title": "Address", "version": "V00172", "icon": "location_on"}, {"id": "9", "title": "Employee", "version": "V00172", "icon": "badge"}, {"id": "10", "title": "New Launch", "version": "V00172", "icon": "rocket_launch"}]}, "gridLayout": {"columns": 12, "gutterWidth": 16, "columnWidth": 80, "maxItemsPerRow": 5, "cardPadding": 12, "sectionPadding": 16}, "styles": {"colors": {"primary": "#3498DB", "secondary": "#2C3E50", "background": "#F8F9FA", "card": "#FFFFFF", "border": "#E2E8F0", "text": {"primary": "#2D3748", "secondary": "#718096", "muted": "#A0AEC0"}, "badge": {"draft": "#FFB900"}}, "radius": {"small": 4, "medium": 8, "large": 12}}}