import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:provider/provider.dart';// Adjust if your path is different

class ChatInputField extends StatelessWidget {
  final TextEditingController chatController;
  final VoidCallback sendMessage;
  final FocusNode? focusNode;

  const ChatInputField({
    super.key,
    required this.chatController,
    required this.sendMessage,
    this.focusNode,
  });

  @override

  Widget build(BuildContext context) {
    final provider = Provider.of<WebHomeProvider>(context);

    return Container(
      margin: EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: Color(0xffd0d0d0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      constraints: BoxConstraints(minHeight: 100),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: chatController,
                    focusNode: focusNode, 
                    onSubmitted: (_) => sendMessage(),
                    decoration: InputDecoration(
                      hintText: 'Ask NSL',
                      hintStyle: TextStyle(color: Colors.grey[400]),
                      border: InputBorder.none,
                      focusedBorder:InputBorder.none ,
                      enabledBorder:InputBorder.none ,
                      contentPadding: EdgeInsets.symmetric(vertical: 10),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Add button
                Container(
                  height: 32,
                  width: 32,
                  decoration: BoxDecoration(
                    color: Color(0xFFE4EDFF),
                    shape: BoxShape.circle,
                  ),
                  padding: EdgeInsets.zero,
                  child: IconButton(
                    icon: Icon(Icons.add, color: Colors.black87),
                    onPressed: () {},
                    iconSize: 16,
                    padding: EdgeInsets.zero,
                  ),
                ),

                // Send/Mic button
                Container(
                  decoration: BoxDecoration(
                    color: Color(0xFFE4EDFF),
                    shape: BoxShape.circle,
                  ),
                  height: 32,
                  width: 32,
                  padding: EdgeInsets.zero,
                  child: IconButton(
                    icon: provider.hasTextInChatField
                        ? Icon(Icons.send, color: Colors.black87)
                        : Icon(Icons.mic, color: Colors.black87),
                    onPressed:
                        provider.hasTextInChatField ? sendMessage : () {},
                    iconSize: 16,
                    padding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
