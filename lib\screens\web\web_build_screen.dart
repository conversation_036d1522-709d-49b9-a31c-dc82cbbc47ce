import 'dart:math';
import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_alert_dialog.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/build_provider.dart';
import '../../models/message.dart';
import '../../models/project_details.dart';
import '../../services/build_service.dart';
import '../../services/speech_service.dart';
import '../../widgets/chat_text_field.dart';
import '../../widgets/full_width_user_bubble.dart';
import '../../widgets/full_width_nsl_bubble.dart';
import '../../widgets/resizable_divider.dart';
import '../../utils/greeting_helper.dart';
import '../../utils/logger.dart';
import '../../widgets/solution_details_panel.dart';
import '../../widgets/web/web_project_details_form.dart';

class WebBuildScreen extends StatefulWidget {
  const WebBuildScreen({super.key});

  @override
  State<WebBuildScreen> createState() => _WebBuildScreenState();
}

class _WebBuildScreenState extends State<WebBuildScreen> {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _focusNode = FocusNode();

  List<Solution> _solutions = [];
  bool _isLoadingSolutions = false;
  final BuildService _buildService = BuildService();
  final SpeechService _speechService = SpeechService();

  // Pastel colors for card backgrounds
  final List<Color> _pastelColors = [
    const Color(0xFFFAEDCB), // Light yellow
    const Color(0xFFC9E4DE), // Mint green
    const Color(0xFFC6DEF1), // Light blue
    const Color(0xFFDBCDF0), // Lavender
    const Color(0xFFF2C6DE), // Pink
    const Color(0xFFF7D9C4), // Peach
  ];

  // Random generator for color selection
  final Random _random = Random();

  // Panel width state
  double _contentWidth = 0.0;
  double _contextPanelWidth = 500.0;
  final double _minContextPanelWidth = 300.0;
  final double _maxContextPanelWidth = 800.0;

  @override
  void initState() {
    super.initState();
    _fetchSolutions();
    // Initialize speech service
    _initializeSpeechService();
  }

  Future<void> _initializeSpeechService() async {
    await _speechService.initialize();
    Logger.info('Speech service initialized in WebBuildScreen');
  }

  Future<void> _speakMessage(String message) async {
    // Check microphone permission first (needed for some devices)
    bool hasPermission = await _speechService.checkMicrophonePermission();
    if (!hasPermission) {
      // Request permission
      hasPermission = await _speechService.requestMicrophonePermission();
      if (!hasPermission) {
        // Show error message if permission denied
        _showPermissionDeniedDialog();
        return;
      }
    }

    Logger.info(
        'Speaking message: ${message.substring(0, message.length > 50 ? 50 : message.length)}...');
    await _speechService.speak(message);
  }

  void _showPermissionDeniedDialog() {
      showDialog(
        context: context,
        builder: (context) => CustomAlertDialog(
          title: 'chat.microphonePermissionRequired',
          content:
              'chat.microphonePermissionMessage',
          onClose: () => Navigator.of(context).pop(),
          primaryButtonText: 'common.ok',
          onPrimaryPressed: () {
            // Handle primary action
            Navigator.of(context).pop();
          },
          // secondaryButtonText: 'Cancel',
          // onSecondaryPressed: () => Navigator.of(context).pop(),
        ),
      );
   
    // showDialog(
    //   context: context,
    //   builder: (BuildContext context) {
    //     return AlertDialog(
    //       title: Text(context.tr('chat.microphonePermissionRequired')),
    //       content: Text(context.tr('chat.microphonePermissionMessage')),
    //       actions: [
    //         TextButton(
    //           onPressed: () => Navigator.of(context).pop(),
    //           child: Text(context.tr('common.ok')),
    //         ),
    //       ],
    //     );
    //   },
    // );
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _fetchSolutions() async {
    setState(() {
      _isLoadingSolutions = true;
    });

    try {
      final solutions = await _buildService.getSolutions();
      setState(() {
        _solutions = solutions;
        _isLoadingSolutions = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingSolutions = false;
      });
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Calculate content width on first build
    if (_contentWidth == 0.0) {
      // Get screen width and subtract sidebar width (70) and context panel width
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final screenWidth = MediaQuery.of(context).size.width;
        setState(() {
          _contentWidth =
              screenWidth - 70 - _contextPanelWidth - 8; // 8 is divider width
        });
      });
    }

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface.withAlpha(245),
      body: Row(
        children: [
          // Navigation Sidebar
          // const WebNavigationSidebar(currentScreen: 'create'),

          // Main Content Area
          SizedBox(
            width: _contentWidth,
            child: Consumer<BuildProvider>(
              builder: (context, buildProvider, _) {
                return buildProvider.showChatView
                    ? _buildChatView()
                    : _buildSolutionsView();
              },
            ),
          ),

          // Resizable Divider
          ResizableDivider(
            onResize: (delta) {
              setState(() {
                // Adjust content width (increase when dragging right, decrease when dragging left)
                // Positive delta means dragging right, negative means dragging left
                _contentWidth =
                    (_contentWidth + delta).clamp(300.0, double.infinity);

                // Calculate new context panel width based on screen width
                final screenWidth = MediaQuery.of(context).size.width;
                _contextPanelWidth =
                    screenWidth - 70 - _contentWidth - 8; // 8 is divider width

                // Ensure context panel width stays within bounds
                if (_contextPanelWidth < _minContextPanelWidth) {
                  _contextPanelWidth = _minContextPanelWidth;
                  _contentWidth = screenWidth - 70 - _contextPanelWidth - 8;
                } else if (_contextPanelWidth > _maxContextPanelWidth) {
                  _contextPanelWidth = _maxContextPanelWidth;
                  _contentWidth = screenWidth - 70 - _contextPanelWidth - 8;
                }
              });
            },
          ),

          // Right Sidebar (Context Panel)
          Container(
            width: _contextPanelWidth,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface.withAlpha(240),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withAlpha(10),
                  blurRadius: 4,
                  offset: const Offset(-2, 0),
                ),
              ],
            ),
            child: _buildContextPanel(),
          ),
        ],
      ),
    );
  }

  Widget _buildSolutionsView() {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            height: 64,
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12.0),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withAlpha(10),
                  blurRadius: 2,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Text(
                  context.tr('build.solutions'),
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Spacer(),
                ElevatedButton.icon(
                  onPressed: () {
                    final buildProvider =
                        Provider.of<BuildProvider>(context, listen: false);
                    buildProvider.clearChat();
                    buildProvider.showProjectDetailsFormView();
                    buildProvider.updateChatViewFlag(true);
                  },
                  icon: Icon(
                    Icons.add,
                    color: Colors.white,
                  ),
                  label: Text(context.tr('build.createSolution')),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 24),

          // Solutions Grid
          Expanded(
            child: _isLoadingSolutions
                ? Center(child: CircularProgressIndicator())
                : _solutions.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.build_outlined,
                              size: 64,
                              color: Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withAlpha(100),
                            ),
                            SizedBox(height: 16),
                            Text(
                              context.tr('build.noSolutions'),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                color: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.color,
                              ),
                            ),
                            SizedBox(height: 24),
                            ElevatedButton.icon(
                              onPressed: () {
                                final buildProvider =
                                    Provider.of<BuildProvider>(context,
                                        listen: false);
                                buildProvider.clearChat();
                                buildProvider.showProjectDetailsFormView();
                                buildProvider.updateChatViewFlag(true);
                              },
                              icon: Icon(Icons.add),
                              label: Text(
                                  context.tr('build.createYourFirstSolution')),
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    Theme.of(context).colorScheme.primary,
                                foregroundColor:
                                    Theme.of(context).colorScheme.onPrimary,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 24, vertical: 12),
                              ),
                            ),
                          ],
                        ),
                      )
                    : LayoutBuilder(
                        builder: (context, constraints) {
                          // Calculate the number of columns based on available width
                          // Minimum card width of 300px
                          final double cardWidth = 300.0;
                          final int crossAxisCount = max(
                              1, (constraints.maxWidth / cardWidth).floor());

                          // Calculate aspect ratio based on available height and width
                          // This makes cards shorter on smaller screens
                          final double screenHeight =
                              MediaQuery.of(context).size.height;
                          final double screenWidth =
                              MediaQuery.of(context).size.width;

                          // Adjust aspect ratio based on screen dimensions
                          // Wider aspect ratio (more width than height) on smaller screens
                          // Default is 1.2 (width is 1.2x the height)
                          double aspectRatio = 1.2;

                          // Make cards shorter on smaller screens
                          if (screenHeight < 800) {
                            aspectRatio =
                                1.6; // Wider cards (less height) on smaller screens
                          } else if (screenWidth < 1400) {
                            aspectRatio =
                                1.4; // Slightly wider cards on medium screens
                          }

                          return GridView.builder(
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: crossAxisCount,
                              crossAxisSpacing: 16,
                              mainAxisSpacing: 16,
                              childAspectRatio: aspectRatio,
                            ),
                            itemCount: _solutions.length,
                            itemBuilder: (context, index) {
                              return _buildSolutionCard(_solutions[index]);
                            },
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSolutionCard(Solution solution) {
    // Select a random pastel color for the card background
    final Color cardColor =
        _pastelColors[_random.nextInt(_pastelColors.length)];

    // Get the first user message as the title, or use a default
    String title = 'Solution';
    if (solution.chatHistory.isNotEmpty) {
      final firstUserMessage = solution.chatHistory.firstWhere(
        (msg) => msg.role == MessageRole.user,
        orElse: () => Message(
            role: MessageRole.user,
            content: 'Solution',
            timestamp: DateTime.now()),
      );
      title = firstUserMessage.content;
      // Truncate if too long
      if (title.length > 50) {
        title = '${title.substring(0, 47)}...';
      }
    } else if (solution.prescriptiveText.isNotEmpty) {
      title = '${solution.prescriptiveText.substring(0, 47)}...';
    }

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: cardColor, // Apply the random pastel color
      child: InkWell(
        onTap: () {
          // Load this solution's chat history
          final buildProvider =
              Provider.of<BuildProvider>(context, listen: false);
          buildProvider.loadSolution(solution);
          buildProvider
              .hideProjectDetailsFormView(); // Ensure project details form is hidden
          buildProvider.updateChatViewFlag(true);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Version badge
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withAlpha(30),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  solution.yamlVersion,
                  maxLines: 1,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              SizedBox(height: 12),

              // Title
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              Spacer(),

              // View button
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    icon: const Icon(Icons.visibility, size: 16),
                    label: Text(context.tr('common.view')),
                    onPressed: () {
                      // Load this solution's chat history
                      final buildProvider =
                          Provider.of<BuildProvider>(context, listen: false);
                      buildProvider.loadSolution(solution);
                      buildProvider
                          .hideProjectDetailsFormView(); // Ensure project details form is hidden
                      buildProvider.updateChatViewFlag(true);
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChatView() {
    return Column(
      children: [
        // Chat Header
        Container(
          height: 64,
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).shadowColor.withAlpha(10),
                blurRadius: 2,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Consumer<BuildProvider>(
                builder: (context, buildProvider, _) {
                  return Text(
                    buildProvider.showProjectDetailsForm
                        ? context.tr('build.newProject')
                        : buildProvider.projectDetails != null
                            ? buildProvider.projectDetails!.projectName
                            : context.tr('build.createSolution'),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  );
                },
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  final buildProvider =
                      Provider.of<BuildProvider>(context, listen: false);
                  buildProvider.clearSelectedMessage();
                  buildProvider.updateChatViewFlag(false);
                },
                tooltip: context.tr('build.backToCreateMenu'),
              ),
              Builder(
                builder: (BuildContext menuContext) {
                  return IconButton(
                    icon: const Icon(Icons.more_vert),
                    onPressed: () {
                      // Get the render box of the button
                      final RenderBox button =
                          menuContext.findRenderObject() as RenderBox;
                      final RenderBox overlay = Overlay.of(menuContext)
                          .context
                          .findRenderObject() as RenderBox;

                      // Calculate position to show menu below the button without overlapping
                      final buttonPos =
                          button.localToGlobal(Offset.zero, ancestor: overlay);
                      final buttonSize = button.size;

                      // Position the menu so it appears directly below the button
                      final RelativeRect position = RelativeRect.fromLTRB(
                        buttonPos.dx, // Left
                        buttonPos.dy +
                            buttonSize.height, // Top (below the button)
                        overlay.size.width -
                            buttonPos.dx -
                            buttonSize.width, // Right
                        overlay.size.height -
                            buttonPos.dy -
                            buttonSize.height, // Bottom
                      );

                      // Show chat options menu below the button
                      showMenu(
                        context: menuContext,
                        position: position,
                        items: [
                          PopupMenuItem(
                            child: Row(
                              children: [
                                Icon(Icons.delete_sweep_outlined, size: 18),
                                const SizedBox(width: 8),
                                Text(context.tr('build.clearChat')),
                              ],
                            ),
                            onTap: () {
                              Provider.of<BuildProvider>(context, listen: false)
                                  .clearChat();
                            },
                          ),
                          PopupMenuItem(
                            child: Row(
                              children: [
                                Icon(Icons.save_outlined, size: 18),
                                const SizedBox(width: 8),
                                Text(context.tr('common.save')),
                              ],
                            ),
                            onTap: () {
                              // Save solution functionality
                            },
                          ),
                          PopupMenuItem(
                            child: Row(
                              children: [
                                Icon(Icons.download_outlined, size: 18),
                                const SizedBox(width: 8),
                                Text(context.tr('build.exportYAML')),
                              ],
                            ),
                            onTap: () {
                              // Export YAML functionality
                            },
                          ),
                        ],
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),

        // Messages Area
        Expanded(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(5),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: _buildMessagesArea(),
          ),
        ),

        // Chat Input - Only show when not displaying project details form
        if (!context.watch<BuildProvider>().showProjectDetailsForm)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ChatTextField(
              controller: _textController,
              hintText: context.tr('chat.chatWithNSL'),
              isLoading: context.watch<BuildProvider>().isLoading,
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  context.read<BuildProvider>().sendMessage(value);
                  _textController.clear();
                  _focusNode.requestFocus();
                }
              },
              onCancel: () => context.read<BuildProvider>().cancelRequest(),
            ),
          ),
      ],
    );
  }

  Widget _buildMessagesArea() {
    return Consumer<BuildProvider>(
      builder: (context, buildProvider, _) {
        // Show project details form if needed
        if (buildProvider.showProjectDetailsForm) {
          return Center(
            child: Container(
              constraints: BoxConstraints(maxWidth: 800),
              child: WebProjectDetailsForm(
                onSubmit: (ProjectDetails details) {
                  // Save project details and continue to chat
                  buildProvider.setProjectDetails(details);
                  buildProvider.hideProjectDetailsFormView();
                },
              ),
            ),
          );
        }

        if (buildProvider.messages.isEmpty && !buildProvider.isLoading) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  children: [
                    if (buildProvider.projectDetails != null) ...[
                      Text(
                        "Project: ${buildProvider.projectDetails!.projectName}",
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color:
                                  Theme.of(context).textTheme.bodyLarge?.color,
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      if (buildProvider.projectDetails!.description != null &&
                          buildProvider.projectDetails!.description!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24.0),
                          child: Text(
                            buildProvider.projectDetails!.description!,
                            style: Theme.of(context).textTheme.bodyMedium,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      const SizedBox(height: 24),
                    ],
                    Text(
                      context.tr('chat.greeting', args: {
                        'greeting': GreetingHelper.getTimeBasedGreeting()
                      }),
                      style: Theme.of(context)
                          .textTheme
                          .headlineMedium
                          ?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Wrap(
                  spacing: 8.0,
                  children: [
                    _SuggestionChip(
                      label: context.tr('build.suggestions.createWorkflow'),
                      onTap: () => _handleSuggestionTap(
                          context.tr('build.examples.createWorkflow')),
                    ),
                    _SuggestionChip(
                      label: context.tr('build.suggestions.generateYAML'),
                      onTap: () => _handleSuggestionTap(
                          context.tr('build.examples.generateYAML')),
                    ),
                    _SuggestionChip(
                      label: context.tr('build.suggestions.buildSolution'),
                      onTap: () => _handleSuggestionTap(
                          context.tr('build.examples.buildSolution')),
                    ),
                  ],
                ),
              ],
            ),
          );
        }

        _scrollToBottom();
        return ClipRRect(
          borderRadius: BorderRadius.circular(12.0),
          child: ListView.builder(
            controller: _scrollController,
            padding:
                const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
            itemCount: buildProvider.messages.length +
                (buildProvider.isLoading ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == buildProvider.messages.length) {
                return const Padding(
                  padding: EdgeInsets.only(top: 16.0),
                  child: Center(child: CircularProgressIndicator()),
                );
              }

              final message = buildProvider.messages[index];
              if (message.role == MessageRole.assistant) {
                return FullWidthNSLBubble(
                  message: message,
                  response: index == buildProvider.messages.length - 1
                      ? buildProvider.lastResponse
                      : null,
                  onLongPress: () => _speakMessage(message.content),
                  //showInRightPanel: true,
                );
              } else {
                return FullWidthUserBubble(message: message);
              }
            },
          ),
        );
      },
    );
  }

  Widget _buildContextPanel() {
    return Consumer<BuildProvider>(
      builder: (context, buildProvider, _) {
        final selectedMessage = buildProvider.selectedMessage;

        return Column(
          children: [
            Expanded(
              child: selectedMessage != null
                  ? SolutionDetailsPanel(
                      message: selectedMessage,
                      response: buildProvider.lastResponse,
                      isBottomSheet: true,
                      onClose: () {
                        buildProvider.clearSelectedMessage();
                      })
                  : Column(
                      children: [
                        Container(
                          height: 64,
                          alignment: Alignment.centerLeft,
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(10),
                                blurRadius: 2,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Text(
                                selectedMessage != null
                                    ? context.tr('build.solutionDetails')
                                    : context.tr('chat.context'),
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8.0, vertical: 4.0),
                                decoration: BoxDecoration(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .primary
                                      .withAlpha(30),
                                  borderRadius: BorderRadius.circular(12.0),
                                ),
                                child: Text(
                                  selectedMessage != null
                                      ? context.tr('build.solution')
                                      : context.tr('chat.aiAssistant'),
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              if (selectedMessage != null)
                                IconButton(
                                  icon: Icon(Icons.close),
                                  onPressed: () {
                                    buildProvider.clearSelectedMessage();
                                  },
                                  tooltip: context.tr('workflow.closeDetails'),
                                ),
                            ],
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(12.0),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(5),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      context.tr('workflow.currentSolution'),
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Container(
                                      padding: const EdgeInsets.all(12.0),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary
                                            .withAlpha(15),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        border: Border.all(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withAlpha(30),
                                          width: 1,
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${context.tr('transaction.status')}:',
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 12),
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            context.tr(
                                                'workflow.noActiveSolution'),
                                            style: TextStyle(fontSize: 12),
                                          ),
                                          Divider(),
                                          Text(
                                            '${context.tr('workflow.components')}:',
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 12),
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            '• ${context.tr('workflow.noComponentsDefined')}',
                                            style: TextStyle(fontSize: 12),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Divider(),
                              Padding(
                                padding: EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      context.tr('workflow.solutionTemplates'),
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      '• ${context.tr('workflow.templates.dataProcessing')}',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      '• ${context.tr('workflow.templates.orderManagement')}',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      '• ${context.tr('workflow.templates.customerFeedback')}',
                                      style: TextStyle(fontSize: 12),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        );
      },
    );
  }

  void _handleSuggestionTap(String suggestion) {
    _textController.text = suggestion;
    _focusNode.requestFocus();
  }
}

class _SuggestionChip extends StatelessWidget {
  final String label;
  final VoidCallback onTap;

  const _SuggestionChip({
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ActionChip(
      label: Text(label),
      onPressed: onTap,
      backgroundColor: Theme.of(context).colorScheme.primary.withAlpha(25),
      labelStyle: TextStyle(color: Theme.of(context).colorScheme.primary),
    );
  }
}
