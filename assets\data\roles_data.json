{"roles": [{"id": "id0001", "title": "CRM Administrator", "description": "Manages system configuration, user accounts, and has full access to all functions.", "version": "V00012", "createdBy": "<PERSON>", "createdDate": "12/04/2023", "modifiedBy": "<PERSON>", "modifiedDate": "25/04/2023", "useCases": ["Can access Claims Processing", "Claims Investigation and Claims Recovery"], "permissions": {"entities": ["Can Read entity Policy", "Can Read entity Customer", "Can Read, Create, Update, Delete entity Claim", "Can Read, Create, Update, Delete entity ClaimNote", "Can Read, Create, Update, Delete entity ClaimDocument", "Can Read, Create, Update, Delete entity ClaimPayment", "Can Read entity Coverage"], "objectives": ["Create <PERSON><PERSON><PERSON>", "Assign <PERSON>", "Review Claim", "Approve Claim", "Reject Claim", "Close Claim", "Initiate Investigation", "Assign <PERSON><PERSON><PERSON><PERSON>or", "Collect Evidence"]}}, {"id": "id0002", "title": "Senior Claims Adjuster", "description": "Handles complex claims, manages claim investigations, and provides technical expertise.", "version": "V00012", "createdBy": "<PERSON>", "createdDate": "12/04/2023", "modifiedBy": "<PERSON>", "modifiedDate": "25/04/2023", "useCases": ["Can access Claims Processing", "Claims Investigation and Claims Recovery"], "permissions": {"entities": ["Can Read entity Policy", "Can Read entity Customer", "Can Read, Create, Update, Delete entity Claim", "Can Read, Create, Update, Delete entity ClaimNote", "Can Read, Create, Update entity ClaimDocument", "Can Read entity ClaimPayment", "Can Read entity Coverage"], "objectives": ["Review Claim", "Approve Claim", "Reject Claim", "Initiate Investigation", "Assign <PERSON><PERSON><PERSON><PERSON>or"]}}, {"id": "id0003", "title": "Clai<PERSON> Adjuster", "description": "Claims Adjuster Processes standard claims, communicates with claimants, and determines coverage.", "version": "V00012", "createdBy": "<PERSON>", "createdDate": "12/04/2023", "modifiedBy": "<PERSON>", "modifiedDate": "25/04/2023", "useCases": ["Can access Claims Processing", "Claims Investigation and Claims Recovery"], "permissions": {"entities": ["Can Read entity Policy", "Can Read entity Customer", "Can Read, Create entity Claim", "Can Read, Create entity ClaimNote", "Can Read entity ClaimDocument", "Can Read entity ClaimPayment", "Can Read entity Coverage"], "objectives": ["Create <PERSON><PERSON><PERSON>", "Review Claim"]}}], "systemInfo": {"roleCount": 3, "hierarchyLevels": 3, "bulletPoints": ["System Administrator has full system access.", "Senior Claims Adjuster has high approval thresholds.", "Claims Adjuster has limited approval authority."]}}