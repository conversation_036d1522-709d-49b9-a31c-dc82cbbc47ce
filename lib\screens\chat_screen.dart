import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_alert_dialog.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../providers/chat_provider.dart';
import '../models/message.dart';
import '../widgets/full_width_user_bubble.dart';
import '../widgets/chat_nsl_bubble.dart';
import '../widgets/full_width_typing_indicator.dart';
import '../widgets/navigation_drawer.dart';
import '../widgets/chat_text_field.dart';
import '../utils/greeting_helper.dart';
import '../theme/spacing.dart';
import '../widgets/responsive_builder.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Helper method to determine device type based on screen width
  DeviceType getDeviceType(double width) {
    if (width < 600) {
      return DeviceType.mobile;
    } else if (width < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        drawer: const AppNavigationDrawer(currentRoute: 'chat'),
        appBar: AppBar(
          title: Text(context.tr('navigation.chat')),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          actions: [
            IconButton(
              icon: const Icon(Icons.delete_outline),
              onPressed: () {
                  showDialog(
        context: context,
        builder: (context) => CustomAlertDialog(
          title: 'chat.clearChat',
          content:
              'chat.clearChatConfirmation',
          onClose: () => Navigator.of(context).pop(),
          primaryButtonText: 'chat.cancel',
          onPrimaryPressed: () {
            // Handle primary action
            Navigator.of(context).pop();
          },
          secondaryButtonText: 'chat.clear',
           onSecondaryPressed: () {
                          Provider.of<ChatProvider>(context, listen: false)
                              .clearChat();
                          Navigator.pop(context);
                        },
        ),
      );
   
                // showDialog(
                //   context: context,
                //   builder: (context) => AlertDialog(
                //     title: Text(context.tr('chat.clearChat')),
                //     content: Text(context.tr('chat.clearChatConfirmation')),
                //     actions: [
                //       TextButton(
                //         onPressed: () => Navigator.pop(context),
                //         child: Text(context.tr('chat.cancel')),
                //       ),
                //       TextButton(
                //         onPressed: () {
                //           Provider.of<ChatProvider>(context, listen: false)
                //               .clearChat();
                //           Navigator.pop(context);
                //         },
                //         child: Text(context.tr('chat.clear')),
                //       ),
                //     ],
                //   ),
                // );
              },
            ),
          ],
        ),
        body: Column(
          children: [
            Expanded(
              child: Consumer<ChatProvider>(
                builder: (context, chatProvider, _) {
                  if (chatProvider.messages.isEmpty &&
                      !chatProvider.isLoading) {
                    // Show greeting when no messages
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // NSL Logo (matching the navigation drawer)
                            // NSLLogo(
                            //   size: 100.0,
                            //   color: Theme.of(context).colorScheme.primary, // Use theme primary color
                            // ),
                            const SizedBox(height: 20),
                            Text(
                              context.tr('chat.greeting', args: {
                                'greeting':
                                    GreetingHelper.getTimeBasedGreeting()
                              }),
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyLarge
                                        ?.color,
                                    fontSize: 28.0,
                                    height: 1.3,
                                    fontFamily: "TiemposText",
                                  ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  // Get device type for responsive spacing
                  final deviceType =
                      getDeviceType(MediaQuery.of(context).size.width);

                  // Show messages when conversation has started
                  _scrollToBottom();
                  return ListView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.symmetric(
                      vertical: AppSpacing.getResponsiveSpacing(
                          AppSpacing.sm, deviceType),
                    ),
                    itemCount: chatProvider.messages.length +
                        (chatProvider.isLoading ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == chatProvider.messages.length) {
                        return FullWidthTypingIndicator(
                          message: context.tr('chat.fetchingAnswer'),
                        );
                      }

                      final message = chatProvider.messages[index];

                      // Use ChatNSLBubble for NSL responses, FullWidthUserBubble for user messages
                      if (message.role == MessageRole.assistant) {
                        return ChatNSLBubble(
                          message: message,
                        );
                      } else {
                        return FullWidthUserBubble(message: message);
                      }
                    },
                  );
                },
              ),
            ),
            Consumer<ChatProvider>(
              builder: (context, chatProvider, _) {
                return ChatTextField(
                  controller: _textController,
                  hintText: context.tr('chat.chatWithNSL'),
                  isLoading: chatProvider.isLoading,
                  onSubmitted: (value) {
                    chatProvider.sendMessage(value);
                  },
                  onCancel: () => chatProvider.cancelRequest(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
